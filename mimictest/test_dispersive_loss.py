#!/usr/bin/env python3
"""
测试脚本：验证Dispersive Loss集成是否正确工作

这个脚本包含了完整的单元测试，确保：
1. TransformerForDiffusion能正确返回特征
2. Chi_Transformer的dispersive loss计算正确
3. DiffusionPolicy能正确集成dispersive loss
4. 梯度能正确反向传播
"""

import torch
import torch.nn as nn
import numpy as np
from mimictest.Nets.Chi_Transformer import Chi_Transformer, TransformerForDiffusion
from mimictest.Wrappers.DiffusionPolicy import DiffusionPolicy

def test_transformer_feature_extraction():
    """测试TransformerForDiffusion的特征提取功能"""
    print("=== 测试TransformerForDiffusion特征提取 ===")
    
    # 创建模型
    model = TransformerForDiffusion(
        input_dim=7,
        output_dim=7,
        max_T=16,
        n_obs_steps=2,
        cond_dim=64,
        n_layer=4,
        n_head=4,
        n_emb=128,
    )
    
    # 创建测试数据
    batch_size = 4
    sample = torch.randn(batch_size, 16, 7)
    timestep = torch.randint(0, 1000, (batch_size,))
    cond = torch.randn(batch_size, 2, 64)
    
    # 测试正常前向传播
    output1 = model(sample, timestep, cond, return_features=False)
    print(f"正常输出形状: {output1.shape}")
    
    # 测试带特征返回的前向传播
    output2, features = model(sample, timestep, cond, return_features=True)
    print(f"带特征输出形状: {output2.shape}")
    print(f"特征形状: {features.shape}")
    
    # 验证输出一致性
    assert torch.allclose(output1, output2, atol=1e-6), "输出应该一致"
    assert features is not None, "特征不应该为None"
    
    print("✅ TransformerForDiffusion特征提取测试通过")
    return True

def test_dispersive_loss_computation():
    """测试dispersive loss计算"""
    print("\n=== 测试Dispersive Loss计算 ===")
    
    # 创建Chi_Transformer
    model = Chi_Transformer(
        camera_num=2,
        obs_horizon=2,
        lowdim_obs_dim=8,
        num_actions=7,
        vision_backbone='resnet18',
        pretrained_backbone_weights=None,
        input_img_shape=[3, 224, 224],
        use_group_norm=True,
        spatial_softmax_num_keypoints=32,
        max_T=16,
        n_layer=4,
        n_head=4,
        n_emb=128,
        p_drop_emb=0.1,
        p_drop_attn=0.1,
        causal_attn=True,
        time_as_cond=True,
        obs_as_cond=True,
        n_cond_layers=2,
        use_dispersive_loss=True,
        dispersive_loss_weight=0.01,
    )
    
    # 测试dispersive loss计算
    batch_size = 8
    feature_dim = 128
    seq_len = 16
    
    # 创建测试特征
    features = torch.randn(batch_size, seq_len, feature_dim)
    
    # 计算dispersive loss
    disp_loss = model.disp_loss(features)
    print(f"Dispersive loss值: {disp_loss.item():.6f}")
    
    # 验证loss是标量
    assert disp_loss.dim() == 0, "Dispersive loss应该是标量"
    assert not torch.isnan(disp_loss), "Dispersive loss不应该是NaN"
    assert not torch.isinf(disp_loss), "Dispersive loss不应该是无穷大"
    
    # 测试空特征情况
    empty_loss = model.disp_loss(None)
    assert empty_loss.item() == 0.0, "空特征的dispersive loss应该为0"
    
    # 测试大批次采样
    large_features = torch.randn(64, seq_len, feature_dim)
    large_disp_loss = model.disp_loss(large_features)
    print(f"大批次Dispersive loss值: {large_disp_loss.item():.6f}")
    
    print("✅ Dispersive Loss计算测试通过")
    return True

def test_chi_transformer_forward():
    """测试Chi_Transformer的前向传播"""
    print("\n=== 测试Chi_Transformer前向传播 ===")
    
    # 创建模型
    model = Chi_Transformer(
        camera_num=2,
        obs_horizon=2,
        lowdim_obs_dim=8,
        num_actions=7,
        vision_backbone='resnet18',
        pretrained_backbone_weights=None,
        input_img_shape=[3, 224, 224],
        use_group_norm=True,
        spatial_softmax_num_keypoints=32,
        max_T=16,
        n_layer=4,
        n_head=4,
        n_emb=128,
        p_drop_emb=0.1,
        p_drop_attn=0.1,
        causal_attn=True,
        time_as_cond=True,
        obs_as_cond=True,
        n_cond_layers=2,
        use_dispersive_loss=True,
        dispersive_loss_weight=0.01,
    )
    
    # 创建测试数据
    batch_size = 4
    batch = {
        'rgb': torch.randn(batch_size, 2, 2, 3, 224, 224),
        'low_dim': torch.randn(batch_size, 2, 8),
        'obs_features': None,
        'noisy_inputs': {'action': torch.randn(batch_size, 16, 7)},
        'timesteps': torch.randint(1, 1000, (batch_size,)),
    }
    
    # 测试正常前向传播
    pred1, obs_features1 = model(batch)
    print(f"正常前向传播 - 预测形状: {pred1['action'].shape}")
    print(f"正常前向传播 - 观察特征形状: {obs_features1.shape}")
    
    # 测试带特征返回的前向传播
    pred2, obs_features2, features = model(batch, return_features=True)
    print(f"带特征前向传播 - 预测形状: {pred2['action'].shape}")
    print(f"带特征前向传播 - 特征形状: {features.shape}")
    
    # 验证输出一致性
    assert torch.allclose(pred1['action'], pred2['action'], atol=1e-6), "预测输出应该一致"
    assert torch.allclose(obs_features1, obs_features2, atol=1e-6), "观察特征应该一致"
    
    print("✅ Chi_Transformer前向传播测试通过")
    return True

def test_diffusion_policy_integration():
    """测试DiffusionPolicy集成"""
    print("\n=== 测试DiffusionPolicy集成 ===")
    
    # 创建Chi_Transformer
    chi_transformer = Chi_Transformer(
        camera_num=2,
        obs_horizon=2,
        lowdim_obs_dim=8,
        num_actions=7,
        vision_backbone='resnet18',
        pretrained_backbone_weights=None,
        input_img_shape=[3, 224, 224],
        use_group_norm=True,
        spatial_softmax_num_keypoints=32,
        max_T=16,
        n_layer=4,
        n_head=4,
        n_emb=128,
        p_drop_emb=0.1,
        p_drop_attn=0.1,
        causal_attn=True,
        time_as_cond=True,
        obs_as_cond=True,
        n_cond_layers=2,
        use_dispersive_loss=True,
        dispersive_loss_weight=0.01,
    )
    
    # 创建DiffusionPolicy
    policy = DiffusionPolicy(
        net=chi_transformer,
        loss_configs={
            'action': {
                'loss_func': nn.MSELoss(),
                'weight': 1.0,
                'type': 'diffusion',
                'shape': (16, 7)
            }
        },
        prediction_type='epsilon',
        scheduler_name='ddpm',
        num_train_steps=1000,
    )
    
    # 创建测试数据
    batch_size = 4
    batch = {
        'rgb': torch.randn(batch_size, 2, 2, 3, 224, 224),
        'low_dim': torch.randn(batch_size, 2, 8),
        'action': torch.randn(batch_size, 16, 7),
    }
    
    # 计算损失
    loss_dict = policy.compute_loss(batch)
    
    print("损失组成:")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.item():.6f}")
    
    # 验证损失组成
    assert 'total_loss' in loss_dict, "应该包含total_loss"
    assert 'action' in loss_dict, "应该包含action loss"
    assert 'dispersive_loss' in loss_dict, "应该包含dispersive_loss"
    
    # 验证dispersive loss被正确添加到total loss中
    expected_total = loss_dict['action'] + chi_transformer.dispersive_loss_weight * loss_dict['dispersive_loss']
    assert torch.allclose(loss_dict['total_loss'], expected_total, atol=1e-6), "Total loss计算错误"
    
    print("✅ DiffusionPolicy集成测试通过")
    return True

def test_gradient_flow():
    """测试梯度流"""
    print("\n=== 测试梯度流 ===")
    
    # 创建模型
    chi_transformer = Chi_Transformer(
        camera_num=1,  # 简化测试
        obs_horizon=1,
        lowdim_obs_dim=4,
        num_actions=3,
        vision_backbone='resnet18',
        pretrained_backbone_weights=None,
        input_img_shape=[3, 64, 64],  # 小图像加速测试
        use_group_norm=True,
        spatial_softmax_num_keypoints=8,
        max_T=8,
        n_layer=2,
        n_head=2,
        n_emb=64,
        p_drop_emb=0.0,  # 关闭dropout确保梯度稳定
        p_drop_attn=0.0,
        causal_attn=True,
        time_as_cond=True,
        obs_as_cond=True,
        n_cond_layers=1,
        use_dispersive_loss=True,
        dispersive_loss_weight=0.1,
    )
    
    policy = DiffusionPolicy(
        net=chi_transformer,
        loss_configs={
            'action': {
                'loss_func': nn.MSELoss(),
                'weight': 1.0,
                'type': 'diffusion',
                'shape': (8, 3)
            }
        },
        prediction_type='epsilon',
        scheduler_name='ddpm',
        num_train_steps=100,
    )
    
    # 创建优化器
    optimizer = torch.optim.Adam(policy.parameters(), lr=1e-4)
    
    # 创建测试数据
    batch = {
        'rgb': torch.randn(2, 1, 1, 3, 64, 64),
        'low_dim': torch.randn(2, 1, 4),
        'action': torch.randn(2, 8, 3),
    }
    
    # 前向传播
    loss_dict = policy.compute_loss(batch)
    total_loss = loss_dict['total_loss']
    
    # 反向传播
    optimizer.zero_grad()
    total_loss.backward()
    
    # 检查梯度
    has_grad = False
    for name, param in policy.named_parameters():
        if param.grad is not None:
            has_grad = True
            grad_norm = param.grad.norm().item()
            if grad_norm > 0:
                print(f"参数 {name} 梯度范数: {grad_norm:.6f}")
                break
    
    assert has_grad, "应该有梯度"
    
    # 执行优化步骤
    optimizer.step()
    
    print("✅ 梯度流测试通过")
    return True

def run_all_tests():
    """运行所有测试"""
    print("开始运行Dispersive Loss集成测试...\n")
    
    tests = [
        test_transformer_feature_extraction,
        test_dispersive_loss_computation,
        test_chi_transformer_forward,
        test_diffusion_policy_integration,
        test_gradient_flow,
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试失败: {test_func.__name__}")
            print(f"错误: {str(e)}")
            failed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{len(tests)}")
    print(f"失败: {failed}/{len(tests)}")
    
    if failed == 0:
        print("🎉 所有测试通过！Dispersive Loss集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查实现")
    
    return failed == 0

if __name__ == "__main__":
    # 设置随机种子确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        print("\n🚀 可以开始使用集成了Dispersive Loss的Chi_Transformer了！")
        print("参考 example_dispersive_loss_usage.py 了解详细使用方法。")
    else:
        print("\n🔧 请修复测试中发现的问题后再使用。")
