# 配置文件：使用Dispersive Loss的Chi_Transformer训练配置
# 这个配置文件展示了如何在实际训练中启用和调整dispersive loss

# 模型配置
model:
  name: "chi_transformer_with_dispersive_loss"
  
  # 基础Transformer参数
  camera_num: 2
  obs_horizon: 2
  lowdim_obs_dim: 8
  num_actions: 7
  
  # 视觉编码器参数
  vision_backbone: "resnet18"
  pretrained_backbone_weights: null
  input_img_shape: [3, 224, 224]
  use_group_norm: true
  spatial_softmax_num_keypoints: 32
  
  # Transformer架构参数
  max_T: 16
  n_layer: 8
  n_head: 8
  n_emb: 512
  p_drop_emb: 0.1
  p_drop_attn: 0.1
  causal_attn: true
  time_as_cond: true
  obs_as_cond: true
  n_cond_layers: 2
  
  # Dispersive Loss参数
  use_dispersive_loss: true
  dispersive_loss_weight: 0.01  # 可以调整这个值来控制dispersive loss的影响

# 训练配置
training:
  # 基础训练参数
  batch_size: 32
  learning_rate: 1e-4
  num_epochs: 100
  
  # 扩散模型参数
  prediction_type: "epsilon"  # 或 "sample"
  scheduler_name: "ddpm"
  num_train_steps: 1000
  
  # 损失函数配置
  loss_configs:
    action:
      loss_func: "mse"
      weight: 1.0
      type: "diffusion"
      
  # Dispersive Loss调度（可选）
  dispersive_loss_schedule:
    # 在训练过程中动态调整dispersive loss权重
    start_weight: 0.001  # 开始时的权重
    end_weight: 0.01     # 结束时的权重
    warmup_epochs: 10    # 权重增加的epoch数

# 数据配置
data:
  dataset_path: "/path/to/your/dataset"
  image_size: [224, 224]
  normalize_images: true
  augmentation: true

# 验证和监控
validation:
  val_freq: 5  # 每5个epoch验证一次
  save_freq: 10  # 每10个epoch保存一次模型
  
  # 监控的指标
  metrics:
    - "total_loss"
    - "action_loss" 
    - "dispersive_loss"  # 监控dispersive loss的值
    
# 日志配置
logging:
  log_dir: "./logs"
  tensorboard: true
  wandb: false
  
  # 记录dispersive loss相关信息
  log_dispersive_loss: true
  log_feature_stats: true  # 记录特征统计信息
