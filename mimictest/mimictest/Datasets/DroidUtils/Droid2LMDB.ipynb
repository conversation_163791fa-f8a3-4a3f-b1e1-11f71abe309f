{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"488f84a4891a4f1c85878acd74013b71": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_60ba5a53ce1a4977b929fc30a6feffbd", "IPY_MODEL_c2c7c17ed6734bb8a3d1d89c6be16b44", "IPY_MODEL_4f7833e7fa59405eac616d73e44de5ea"], "layout": "IPY_MODEL_7c83209ec0594adba05dfa7a861e2a96"}}, "60ba5a53ce1a4977b929fc30a6feffbd": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b1c66037204f43518b810dff5bd00b94", "placeholder": "​", "style": "IPY_MODEL_131c51ba6d794740b443c31ae01f9bd5", "value": "preprocessor_config.json: 100%"}}, "c2c7c17ed6734bb8a3d1d89c6be16b44": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_07b36c33bc0449159b62c7ebc117d772", "max": 806, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1e104ed42f5b4347b7f452de8a84cdc1", "value": 806}}, "4f7833e7fa59405eac616d73e44de5ea": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4597b22dba0c48609c6c5e079725a09f", "placeholder": "​", "style": "IPY_MODEL_d7ee3c2491b3433caed000b174bc08cc", "value": " 806/806 [00:00&lt;00:00, 37.4kB/s]"}}, "7c83209ec0594adba05dfa7a861e2a96": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1c66037204f43518b810dff5bd00b94": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "131c51ba6d794740b443c31ae01f9bd5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "07b36c33bc0449159b62c7ebc117d772": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1e104ed42f5b4347b7f452de8a84cdc1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4597b22dba0c48609c6c5e079725a09f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d7ee3c2491b3433caed000b174bc08cc": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "58060f71cfa34bc7a035f27b01c986d2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5ea3418050ba434596d4105b01546d11", "IPY_MODEL_6ef8dab0e6b5494fab979108fe6de600", "IPY_MODEL_fe13c24c738443348e200618cebd9bc5"], "layout": "IPY_MODEL_b77bfe16981142b29b9fabafb2177a2c"}}, "5ea3418050ba434596d4105b01546d11": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_358ae773661e426c97e98305a695b942", "placeholder": "​", "style": "IPY_MODEL_46836f54710743659e6a3916cba9690d", "value": "processing_florence2.py: 100%"}}, "6ef8dab0e6b5494fab979108fe6de600": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6dfba0b389af470ba9e327a07b951038", "max": 46372, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_40742fe296254abd8f007f513e9a2b5a", "value": 46372}}, "fe13c24c738443348e200618cebd9bc5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f30460f03f0646cd92b6cf2760e268d4", "placeholder": "​", "style": "IPY_MODEL_8e96f1aee5804d2ba9708875d7fefaf0", "value": " 46.4k/46.4k [00:00&lt;00:00, 2.22MB/s]"}}, "b77bfe16981142b29b9fabafb2177a2c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "358ae773661e426c97e98305a695b942": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "46836f54710743659e6a3916cba9690d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6dfba0b389af470ba9e327a07b951038": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "40742fe296254abd8f007f513e9a2b5a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f30460f03f0646cd92b6cf2760e268d4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8e96f1aee5804d2ba9708875d7fefaf0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7293eafb17fd4438a0487cd6a8319c1b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0af2b8e7257a4af09e1065f45961b1cc", "IPY_MODEL_b7a4cafc303742f1bda4b6fcc81d9880", "IPY_MODEL_9538a54823384da1affe6c6f74a49471"], "layout": "IPY_MODEL_bdc1d1a0a8f545eaa1ed4cdbdd0804a7"}}, "0af2b8e7257a4af09e1065f45961b1cc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_532461f7ca354b26825138ba2685b2a3", "placeholder": "​", "style": "IPY_MODEL_4dc69e62e8b74e9b8eccc35572bb057f", "value": "tokenizer_config.json: 100%"}}, "b7a4cafc303742f1bda4b6fcc81d9880": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7693f564c5894f66a52fbeebfa7309e7", "max": 34, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_85596ca8f97644c39b7e7b37fe3ce026", "value": 34}}, "9538a54823384da1affe6c6f74a49471": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7f6845f0b81347f69f37c23d3cf9ac13", "placeholder": "​", "style": "IPY_MODEL_a32ca2aafb82427eb2d35532fd583789", "value": " 34.0/34.0 [00:00&lt;00:00, 1.81kB/s]"}}, "bdc1d1a0a8f545eaa1ed4cdbdd0804a7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "532461f7ca354b26825138ba2685b2a3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4dc69e62e8b74e9b8eccc35572bb057f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7693f564c5894f66a52fbeebfa7309e7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "85596ca8f97644c39b7e7b37fe3ce026": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7f6845f0b81347f69f37c23d3cf9ac13": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a32ca2aafb82427eb2d35532fd583789": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1b75c0c2a84647658fa538559eab9236": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_87b17cabdb424413a056e8e1a9ac0616", "IPY_MODEL_9b0abd58ac9c4bfc8fe1b48a1af99efd", "IPY_MODEL_092f70d765fb4b2f9ab1defff4a1fe6a"], "layout": "IPY_MODEL_9e215af0dc6446b08160c4959da8892d"}}, "87b17cabdb424413a056e8e1a9ac0616": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_713602b9bfeb4d46a51c8f3e4d96f776", "placeholder": "​", "style": "IPY_MODEL_326cf4ff3ebb43d089b778317d40cfdb", "value": "vocab.json: 100%"}}, "9b0abd58ac9c4bfc8fe1b48a1af99efd": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2fa1515e9fed487ba17cea7c3411a3f1", "max": 1099884, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4bf23941f7a242d487d060c79f12603c", "value": 1099884}}, "092f70d765fb4b2f9ab1defff4a1fe6a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7e67431e32d54a70a9175ac25434ab39", "placeholder": "​", "style": "IPY_MODEL_318b98504de345ccb16ba09f3d3e7691", "value": " 1.10M/1.10M [00:00&lt;00:00, 4.46MB/s]"}}, "9e215af0dc6446b08160c4959da8892d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "713602b9bfeb4d46a51c8f3e4d96f776": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "326cf4ff3ebb43d089b778317d40cfdb": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2fa1515e9fed487ba17cea7c3411a3f1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4bf23941f7a242d487d060c79f12603c": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7e67431e32d54a70a9175ac25434ab39": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "318b98504de345ccb16ba09f3d3e7691": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "91ea5a634d134e8cae44db4e3aa7ba4a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8bf46dbec4584d41a3978894bef72c4c", "IPY_MODEL_470cfab637324e11ad013bb5d658fd35", "IPY_MODEL_d7ddb9dc945542b2a867382f8a6a2b31"], "layout": "IPY_MODEL_9a07a7a3a12348e78e8666b8bf36c5d8"}}, "8bf46dbec4584d41a3978894bef72c4c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1a1eb45c87784ca486d57ff65c1cfaa3", "placeholder": "​", "style": "IPY_MODEL_59b44fa88ce5468a9d240fe37c728a09", "value": "tokenizer.json: 100%"}}, "470cfab637324e11ad013bb5d658fd35": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3d3a8d5b7efd478ca8a4a64cf1ab9fca", "max": 1355863, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f380fde41d9b4449b2b4566854c6ade1", "value": 1355863}}, "d7ddb9dc945542b2a867382f8a6a2b31": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_80347e302f1148528120e81c834e05b4", "placeholder": "​", "style": "IPY_MODEL_ce064362225a40cfac407daf315d0137", "value": " 1.36M/1.36M [00:00&lt;00:00, 4.14MB/s]"}}, "9a07a7a3a12348e78e8666b8bf36c5d8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a1eb45c87784ca486d57ff65c1cfaa3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "59b44fa88ce5468a9d240fe37c728a09": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3d3a8d5b7efd478ca8a4a64cf1ab9fca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f380fde41d9b4449b2b4566854c6ade1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "80347e302f1148528120e81c834e05b4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce064362225a40cfac407daf315d0137": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "df1ae8be6b5649508c5fbfc7e1497dca": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2ff0ab7430d44b01b2abc2a9a9caabfc", "IPY_MODEL_d2a3b340d98c439a89b90574c91ceb98", "IPY_MODEL_d76392200b4b458ab39ab70415f01b4e"], "layout": "IPY_MODEL_b1958cf702d34065b8ec42270649ae9a"}}, "2ff0ab7430d44b01b2abc2a9a9caabfc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e80b283ccef64ed1be9b106286ac0812", "placeholder": "​", "style": "IPY_MODEL_e51862645cea4fb3b00d71fcd453af2e", "value": "config.json: 100%"}}, "d2a3b340d98c439a89b90574c91ceb98": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_58444f4e20d34305a68c1eb1ea0e5735", "max": 2430, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e1f1c6cb1a1a463f85767f564b735890", "value": 2430}}, "d76392200b4b458ab39ab70415f01b4e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f4fc5bfc019343659cc7cf44ccbccd91", "placeholder": "​", "style": "IPY_MODEL_500a66f777a34deaa0b32b11b0a58c39", "value": " 2.43k/2.43k [00:00&lt;00:00, 173kB/s]"}}, "b1958cf702d34065b8ec42270649ae9a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e80b283ccef64ed1be9b106286ac0812": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e51862645cea4fb3b00d71fcd453af2e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "58444f4e20d34305a68c1eb1ea0e5735": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e1f1c6cb1a1a463f85767f564b735890": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f4fc5bfc019343659cc7cf44ccbccd91": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "500a66f777a34deaa0b32b11b0a58c39": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0b86cf575e1849e7b96ffc5b1f335aa2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f7d90a23a3d34be8ad96d78b627929b5", "IPY_MODEL_772c9211d21647e6b47d0c62df007d78", "IPY_MODEL_46d81d4fb5d44555a20b973f6ce4782c"], "layout": "IPY_MODEL_d1fc7269c5064fa4a168f47be6a593f8"}}, "f7d90a23a3d34be8ad96d78b627929b5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c6126e3bd51e4f5091558a853aece022", "placeholder": "​", "style": "IPY_MODEL_2aee8fca1366451f8edbd77eeaf37a10", "value": "configuration_florence2.py: 100%"}}, "772c9211d21647e6b47d0c62df007d78": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_606631ca61a3454fa21e4b35b2da4f46", "max": 15125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_763def28f3c3487e904fff25f238787f", "value": 15125}}, "46d81d4fb5d44555a20b973f6ce4782c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d054e36d70604d35a15f355952145e32", "placeholder": "​", "style": "IPY_MODEL_1af9af8a368e482a87abb6d8e18ef910", "value": " 15.1k/15.1k [00:00&lt;00:00, 633kB/s]"}}, "d1fc7269c5064fa4a168f47be6a593f8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6126e3bd51e4f5091558a853aece022": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2aee8fca1366451f8edbd77eeaf37a10": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "606631ca61a3454fa21e4b35b2da4f46": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "763def28f3c3487e904fff25f238787f": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d054e36d70604d35a15f355952145e32": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1af9af8a368e482a87abb6d8e18ef910": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "source": ["!pip install lmdb torchvision"], "metadata": {"id": "bHVZQedQhRHk"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!gsutil -m cp -r gs://gresearch/robotics/droid_100 ."], "metadata": {"id": "Rpf46SMnCRrE", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "aaa05800-0f87-4a0d-d8b8-f72960ae1fb6"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00016-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00017-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00018-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00019-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00020-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00021-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00022-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00023-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00024-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00025-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00026-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00027-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00028-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00029-of-00031...\n", "Copying gs://gresearch/robotics/droid_100/1.0.0/r2d2_faceblur-train.tfrecord-00030-of-00031...\n", "- [33/33 files][  2.0 GiB/  2.0 GiB] 100% Done  87.3 MiB/s ETA 00:00:00         \n", "Operation completed over 33 objects/2.0 GiB.                                     \n"]}]}, {"cell_type": "code", "source": ["import tensorflow_datasets as tfds\n", "\n", "tf_builder = tfds.builder_from_directory(builder_dir=\"./droid_100/1.0.0\")\n", "tf_dataset = tf_builder.as_dataset(split=\"train\")"], "metadata": {"id": "BvnPvUyMhfXq"}, "execution_count": 3, "outputs": []}, {"cell_type": "markdown", "source": ["Filter out failure trajectories and flatten the dataset"], "metadata": {"id": "XYz6VP66hxHE"}}, {"cell_type": "code", "source": ["import tensorflow as tf\n", "\n", "def filter_success(trajectory: dict[str, any]):\n", "  file_paths = trajectory['episode_metadata']['file_path']\n", "  return tf.strings.regex_full_match(\n", "   file_paths,\n", "   \".*/success/.*\"\n", "  )\n", "tf_dataset = tf_dataset.filter(filter_success)\n", "tf_dataset = tf_dataset.flat_map(lambda episode: episode['steps']) # Extract steps from episodes"], "metadata": {"id": "8n-2OxGZmLaV"}, "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "source": ["Filter out IS_LAST steps, since in RLDS this contains just the last observation where the action and reward are meaningless."], "metadata": {"id": "fk7h4fKggW2y"}}, {"cell_type": "code", "source": ["def _is_not_last(step):\n", "  if step['is_last']:\n", "    return False\n", "  return True\n", "\n", "tf_dataset = tf_dataset.filter(_is_not_last)"], "metadata": {"id": "0V0C89bChD5b"}, "execution_count": 5, "outputs": []}, {"cell_type": "markdown", "source": ["Save data in lmdb"], "metadata": {"id": "0G_gFFe3p2EX"}}, {"cell_type": "code", "source": ["import os\n", "import json\n", "from pathlib import Path\n", "import torch\n", "from torchvision.io import encode_jpeg\n", "import lmdb\n", "from pickle import dumps\n", "\n", "lmdb_dir = Path(\"./droid_lmdb\")\n", "single_file_episode_num = 30 # to be changed!\n", "\n", "if not os.path.exists(lmdb_dir):\n", "    os.makedirs(lmdb_dir)\n", "split_id = -1\n", "max_steps = [] # step ID of the last sample in a split\n", "cur_episode = -1\n", "cur_step = -1\n", "\n", "def to_numpy(mydict):\n", "  for term in mydict:\n", "    if isinstance(mydict[term], dict):\n", "      mydict[term] = to_numpy(mydict[term])\n", "    else:\n", "      mydict[term] = mydict[term].numpy()\n", "  return mydict\n", "\n", "tf_dataset = iter(tf_dataset)\n", "while True:\n", "  try:\n", "    step_data = next(tf_dataset)\n", "    cur_step += 1\n", "  except StopIteration:\n", "    max_steps.append(cur_step)\n", "    json.dump(max_steps, open(lmdb_dir/'split.json', 'w'))\n", "    txn.commit()\n", "    env.close()\n", "    break\n", "  if step_data['is_first'] == True or cur_episode == -1:\n", "    cur_episode += 1\n", "    print('Saving episode ', cur_episode)\n", "    if cur_episode % single_file_episode_num == 0:\n", "      split_id += 1\n", "      if cur_episode != 0:\n", "        max_steps.append(cur_step)\n", "        json.dump(max_steps, open(lmdb_dir/'split.json', 'w'))\n", "        txn.commit()\n", "        env.close()\n", "      print('Create split ', split_id, ' in episode ', cur_episode)\n", "      env = lmdb.open(str(lmdb_dir/str(split_id)), map_size=int(3e12), readonly=False, lock=False) # maximum size of memory map is 3TB\n", "      txn = env.begin(write=True)\n", "    lang = []\n", "    if step_data['language_instruction'].numpy() != b'':\n", "      lang.append(step_data['language_instruction'].numpy())\n", "    if step_data['language_instruction_2'].numpy() != b'':\n", "      lang.append(step_data['language_instruction_2'].numpy())\n", "    if step_data['language_instruction_3'].numpy() != b'':\n", "      lang.append(step_data['language_instruction_3'].numpy())\n", "    txn.put(f'lang_{cur_episode}'.encode(), dumps(lang))\n", "  if len(step_data['observation']['exterior_image_1_left'].shape) != 3:\n", "    continue # may be a problem!!!\n", "  img = {}\n", "  img['exterior_image_1_left'] = encode_jpeg(torch.from_numpy(step_data['observation']['exterior_image_1_left'].numpy()).permute(2,0,1))\n", "  img['exterior_image_2_left'] = encode_jpeg(torch.from_numpy(step_data['observation']['exterior_image_2_left'].numpy()).permute(2,0,1))\n", "  img['wrist_image_left'] = encode_jpeg(torch.from_numpy(step_data['observation']['wrist_image_left'].numpy()).permute(2,0,1))\n", "  del step_data['observation']['exterior_image_1_left']\n", "  del step_data['observation']['exterior_image_2_left']\n", "  del step_data['observation']['wrist_image_left']\n", "  del step_data['language_instruction']\n", "  del step_data['language_instruction_2']\n", "  del step_data['language_instruction_3']\n", "  others = to_numpy(step_data)\n", "  txn.put(f'cur_episode_{cur_step}'.encode(), dumps(cur_episode))\n", "  txn.put(f'others_{cur_step}'.encode(), dumps(others))\n", "  txn.put(f'img_{cur_step}'.encode(), dumps(img))"], "metadata": {"id": "PcQX0tHzDkDs", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "fb712b64-5a4d-4cc7-f562-9566fa1e167a"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Saving episode  0\n", "C<PERSON> split  0  in episode  0\n", "Saving episode  1\n", "Saving episode  2\n", "Saving episode  3\n", "Saving episode  4\n", "Saving episode  5\n", "Saving episode  6\n", "Saving episode  7\n", "Saving episode  8\n", "Saving episode  9\n", "Saving episode  10\n", "Saving episode  11\n", "Saving episode  12\n", "Saving episode  13\n", "Saving episode  14\n", "Saving episode  15\n", "Saving episode  16\n", "Saving episode  17\n", "Saving episode  18\n", "Saving episode  19\n", "Saving episode  20\n", "Saving episode  21\n", "Saving episode  22\n", "Saving episode  23\n", "Saving episode  24\n", "Saving episode  25\n", "Saving episode  26\n", "Saving episode  27\n", "Saving episode  28\n", "Saving episode  29\n", "Saving episode  30\n", "Create split  1  in episode  30\n", "Saving episode  31\n", "Saving episode  32\n", "Saving episode  33\n", "Saving episode  34\n", "Saving episode  35\n", "Saving episode  36\n", "Saving episode  37\n", "Saving episode  38\n", "Saving episode  39\n", "Saving episode  40\n", "Saving episode  41\n", "Saving episode  42\n", "Saving episode  43\n", "Saving episode  44\n", "Saving episode  45\n", "Saving episode  46\n", "Saving episode  47\n", "Saving episode  48\n", "Saving episode  49\n", "Saving episode  50\n", "Saving episode  51\n", "Saving episode  52\n", "Saving episode  53\n", "Saving episode  54\n", "Saving episode  55\n", "Saving episode  56\n", "Saving episode  57\n", "Saving episode  58\n", "Saving episode  59\n", "Saving episode  60\n", "C<PERSON> split  2  in episode  60\n", "Saving episode  61\n", "Saving episode  62\n", "Saving episode  63\n", "Saving episode  64\n", "Saving episode  65\n", "Saving episode  66\n", "Saving episode  67\n", "Saving episode  68\n", "Saving episode  69\n", "Saving episode  70\n", "Saving episode  71\n", "Saving episode  72\n", "Saving episode  73\n", "Saving episode  74\n", "Saving episode  75\n", "Saving episode  76\n", "Saving episode  77\n", "Saving episode  78\n", "Saving episode  79\n", "Saving episode  80\n"]}]}, {"cell_type": "code", "source": ["!rm -rf ./droid_lmdb"], "metadata": {"id": "D8FPXRGCUtoG"}, "execution_count": 63, "outputs": []}, {"cell_type": "markdown", "source": ["Define Droid lmdb reader class"], "metadata": {"id": "Ep3k4uAejU90"}}, {"cell_type": "code", "source": ["import json\n", "from pathlib import Path\n", "import lmdb\n", "from tqdm import tqdm\n", "from torchvision.io import decode_jpeg\n", "from pickle import loads\n", "\n", "class DroidReader():\n", "\n", "    def __init__(self, lmdb_dir):\n", "        if isinstance(lmdb_dir, str):\n", "            lmdb_dir = Path(lmdb_dir)\n", "        self.lmdb_dir = lmdb_dir\n", "        self.envs = []\n", "        self.txns = []\n", "        self.max_steps = json.load(open(lmdb_dir/'split.json', 'r'))\n", "        split_num = len(self.max_steps)\n", "        self.min_steps = [0] + [self.max_steps[split_id]+1 for split_id in range(split_num-1)]\n", "        self.dataset_len = self.max_steps[-1] + 1\n", "\n", "    def __len__(self):\n", "        return self.dataset_len\n", "\n", "    def open_lmdb(self, write=False):\n", "        for split_id, split in enumerate(self.max_steps):\n", "            split_path = self.lmdb_dir / str(split_id)\n", "            env = lmdb.open(str(split_path), readonly=not write, create=False, lock=False, map_size=int(3e12))\n", "            txn = env.begin(write=write)\n", "            self.envs.append(env)\n", "            self.txns.append(txn)\n", "\n", "    def close_lmdb(self):\n", "        for txn in self.txns:\n", "            txn.commit()\n", "        for env in self.envs:\n", "            env.close()\n", "        self.envs = []\n", "        self.txns = []\n", "\n", "    def get_split_id(self, idx, array):\n", "        left, right = 0, len(self.max_steps) - 1\n", "        while left < right:\n", "            mid = (left + right) // 2\n", "            if array[mid] > idx:\n", "                right = mid\n", "            else:\n", "                left = mid + 1\n", "        return left\n", "\n", "    def get_episode(self, idx):\n", "        if self.envs == []:\n", "            self.open_lmdb()\n", "        split_id = self.get_split_id(idx, self.max_steps)\n", "        cur_episode = loads(self.txns[split_id].get(f'cur_episode_{idx}'.encode()))\n", "        return cur_episode\n", "\n", "    def get_img(self, idx):\n", "        if self.envs == []:\n", "            self.open_lmdb()\n", "        split_id = self.get_split_id(idx, self.max_steps)\n", "        img = loads(self.txns[split_id].get(f'img_{idx}'.encode()))\n", "        img['exterior_image_1_left'] = decode_jpeg(img['exterior_image_1_left'])\n", "        img['exterior_image_2_left'] = decode_jpeg(img['exterior_image_2_left'])\n", "        img['wrist_image_left'] = decode_jpeg(img['wrist_image_left'])\n", "        return img\n", "\n", "    def get_langs(self, idx):\n", "        if self.envs == []:\n", "            self.open_lmdb()\n", "        split_id = self.get_split_id(idx, self.max_steps)\n", "        ep_id = self.get_episode(idx)\n", "        langs = loads(self.txns[split_id].get(f'lang_{ep_id}'.encode()))\n", "        return langs\n", "\n", "    def get_lang_tokens(self, idx):\n", "        if self.envs == []:\n", "            self.open_lmdb()\n", "        split_id = self.get_split_id(idx, self.max_steps)\n", "        ep_id = self.get_episode(idx)\n", "        lang_tokens = loads(self.txns[split_id].get(f'lang_token_{ep_id}'.encode()))\n", "        return lang_tokens\n", "\n", "    def get_others(self, idx):\n", "        if self.envs == []:\n", "            self.open_lmdb()\n", "        split_id = self.get_split_id(idx, self.max_steps)\n", "        others = loads(self.txns[split_id].get(f'others_{idx}'.encode()))\n", "        return others\n", "\n", "    def write_lang_token_id(self, tokenizer):\n", "        if self.envs == []:\n", "            self.open_lmdb(write=True)\n", "        last_episode_id = -1\n", "        for idx in tqdm(range(self.dataset_len - 1), desc=\"Write lang token id\"):\n", "            episode_id = self.get_episode(idx)\n", "            if episode_id != last_episode_id:\n", "                langs = self.get_langs(idx)\n", "                lang_tokens = []\n", "                for lang in langs:\n", "                    lang_token = tokenizer(\n", "                        lang.decode('utf-8'),\n", "                        return_tensors=\"pt\",\n", "                        padding=False,\n", "                        max_length=None,\n", "                        truncation=None,\n", "                        return_token_type_ids=False,\n", "                    )['input_ids'][0].numpy()\n", "                    lang_tokens.append(lang_token)\n", "                split_id = self.get_split_id(idx, self.max_steps)\n", "                self.txns[split_id].put(\n", "                    f'lang_token_{episode_id}'.encode(),\n", "                    dumps(lang_tokens),\n", "                )\n", "                last_episode_id = episode_id\n", "        self.close_lmdb()"], "metadata": {"id": "nrGq0LOjjUQ7"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["reader = DroidReader(lmdb_dir)\n", "from transformers import AutoProcessor\n", "os.environ['TOKENIZERS_PARALLELISM'] = 'true'\n", "tokenizer = AutoProcessor.from_pretrained(\"microsoft/Florence-2-base\", trust_remote_code=True).tokenizer\n", "reader.write_lang_token_id(tokenizer)"], "metadata": {"id": "WbRZf1Q8fNK2", "colab": {"base_uri": "https://localhost:8080/", "height": 487, "referenced_widgets": ["488f84a4891a4f1c85878acd74013b71", "60ba5a53ce1a4977b929fc30a6feffbd", "c2c7c17ed6734bb8a3d1d89c6be16b44", "4f7833e7fa59405eac616d73e44de5ea", "7c83209ec0594adba05dfa7a861e2a96", "b1c66037204f43518b810dff5bd00b94", "131c51ba6d794740b443c31ae01f9bd5", "07b36c33bc0449159b62c7ebc117d772", "1e104ed42f5b4347b7f452de8a84cdc1", "4597b22dba0c48609c6c5e079725a09f", "d7ee3c2491b3433caed000b174bc08cc", "58060f71cfa34bc7a035f27b01c986d2", "5ea3418050ba434596d4105b01546d11", "6ef8dab0e6b5494fab979108fe6de600", "fe13c24c738443348e200618cebd9bc5", "b77bfe16981142b29b9fabafb2177a2c", "358ae773661e426c97e98305a695b942", "46836f54710743659e6a3916cba9690d", "6dfba0b389af470ba9e327a07b951038", "40742fe296254abd8f007f513e9a2b5a", "f30460f03f0646cd92b6cf2760e268d4", "8e96f1aee5804d2ba9708875d7fefaf0", "7293eafb17fd4438a0487cd6a8319c1b", "0af2b8e7257a4af09e1065f45961b1cc", "b7a4cafc303742f1bda4b6fcc81d9880", "9538a54823384da1affe6c6f74a49471", "bdc1d1a0a8f545eaa1ed4cdbdd0804a7", "532461f7ca354b26825138ba2685b2a3", "4dc69e62e8b74e9b8eccc35572bb057f", "7693f564c5894f66a52fbeebfa7309e7", "85596ca8f97644c39b7e7b37fe3ce026", "7f6845f0b81347f69f37c23d3cf9ac13", "a32ca2aafb82427eb2d35532fd583789", "1b75c0c2a84647658fa538559eab9236", "87b17cabdb424413a056e8e1a9ac0616", "9b0abd58ac9c4bfc8fe1b48a1af99efd", "092f70d765fb4b2f9ab1defff4a1fe6a", "9e215af0dc6446b08160c4959da8892d", "713602b9bfeb4d46a51c8f3e4d96f776", "326cf4ff3ebb43d089b778317d40cfdb", "2fa1515e9fed487ba17cea7c3411a3f1", "4bf23941f7a242d487d060c79f12603c", "7e67431e32d54a70a9175ac25434ab39", "318b98504de345ccb16ba09f3d3e7691", "91ea5a634d134e8cae44db4e3aa7ba4a", "8bf46dbec4584d41a3978894bef72c4c", "470cfab637324e11ad013bb5d658fd35", "d7ddb9dc945542b2a867382f8a6a2b31", "9a07a7a3a12348e78e8666b8bf36c5d8", "1a1eb45c87784ca486d57ff65c1cfaa3", "59b44fa88ce5468a9d240fe37c728a09", "3d3a8d5b7efd478ca8a4a64cf1ab9fca", "f380fde41d9b4449b2b4566854c6ade1", "80347e302f1148528120e81c834e05b4", "ce064362225a40cfac407daf315d0137", "df1ae8be6b5649508c5fbfc7e1497dca", "2ff0ab7430d44b01b2abc2a9a9caabfc", "d2a3b340d98c439a89b90574c91ceb98", "d76392200b4b458ab39ab70415f01b4e", "b1958cf702d34065b8ec42270649ae9a", "e80b283ccef64ed1be9b106286ac0812", "e51862645cea4fb3b00d71fcd453af2e", "58444f4e20d34305a68c1eb1ea0e5735", "e1f1c6cb1a1a463f85767f564b735890", "f4fc5bfc019343659cc7cf44ccbccd91", "500a66f777a34deaa0b32b11b0a58c39", "0b86cf575e1849e7b96ffc5b1f335aa2", "f7d90a23a3d34be8ad96d78b627929b5", "772c9211d21647e6b47d0c62df007d78", "46d81d4fb5d44555a20b973f6ce4782c", "d1fc7269c5064fa4a168f47be6a593f8", "c6126e3bd51e4f5091558a853aece022", "2aee8fca1366451f8edbd77eeaf37a10", "606631ca61a3454fa21e4b35b2da4f46", "763def28f3c3487e904fff25f238787f", "d054e36d70604d35a15f355952145e32", "1af9af8a368e482a87abb6d8e18ef910"]}, "outputId": "b2b93922-da51-4a03-e957-b728c0deef95"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_auth.py:94: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["preprocessor_config.json:   0%|          | 0.00/806 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "488f84a4891a4f1c85878acd74013b71"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["processing_florence2.py:   0%|          | 0.00/46.4k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "58060f71cfa34bc7a035f27b01c986d2"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["A new version of the following files was downloaded from https://huggingface.co/microsoft/Florence-2-base:\n", "- processing_florence2.py\n", ". Make sure to double-check they do not contain any added malicious code. To avoid downloading new versions of the code file, you can pin a revision.\n"]}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/34.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "7293eafb17fd4438a0487cd6a8319c1b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.json:   0%|          | 0.00/1.10M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1b75c0c2a84647658fa538559eab9236"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/1.36M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "91ea5a634d134e8cae44db4e3aa7ba4a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/2.43k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "df1ae8be6b5649508c5fbfc7e1497dca"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["configuration_florence2.py:   0%|          | 0.00/15.1k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "0b86cf575e1849e7b96ffc5b1f335aa2"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["A new version of the following files was downloaded from https://huggingface.co/microsoft/Florence-2-base:\n", "- configuration_florence2.py\n", ". Make sure to double-check they do not contain any added malicious code. To avoid downloading new versions of the code file, you can pin a revision.\n", "Write lang token id: 100%|██████████| 28542/28542 [00:00<00:00, 259763.49it/s]\n"]}]}, {"cell_type": "code", "source": ["length = len(reader)\n", "episode = reader.get_episode(28541)\n", "img = reader.get_img(28541)\n", "others = reader.get_others(28541)\n", "langs = reader.get_langs(10)\n", "lang_tokens = reader.get_lang_tokens(10)"], "metadata": {"id": "uZIn5nQ5ysN0"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["langs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-8Klf4qE249F", "outputId": "b496a87e-3ad7-4a05-92ff-c8a79917c789"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[b'Put the marker in the pot',\n", " b'Get the marker from the table and put it inside the silver pot',\n", " b'Put the marker inside the silver pot']"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["lang_tokens"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VFlNYQlk2GPs", "outputId": "90f7c7fe-b4b3-4712-f767-7aaf4bb59efc"}, "execution_count": 11, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[array([    0, 35123,     5, 17540,    11,     5,  4728,     2]),\n", " array([    0, 14181,     5, 17540,    31,     5,  2103,     8,   342,\n", "           24,  1025,     5,  4334,  4728,     2]),\n", " array([    0, 35123,     5, 17540,  1025,     5,  4334,  4728,     2])]"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["others"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8n7NI3V-6M_t", "outputId": "b2fa0dd9-587d-45b9-cf50-c40179b1f469"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'action': array([ 0.46315864, -0.06332067,  0.48752004, -2.5370121 , -0.30514622,\n", "        -1.15552211,  0.        ]),\n", " 'action_dict': {'cartesian_position': array([ 0.46315864, -0.06332067,  0.48752004, -2.5370121 , -0.30514622,\n", "         -1.15552211]),\n", "  'cartesian_velocity': array([0., 0., 0., 0., 0., 0.]),\n", "  'gripper_position': array([0.]),\n", "  'gripper_velocity': array([0.]),\n", "  'joint_position': array([ 0.21305765, -0.378001  , -0.3413465 , -2.50903964, -0.08747865,\n", "          2.81095266,  0.98531276]),\n", "  'joint_velocity': array([-0.73450541,  0.14465635,  0.75720179, -0.03155373,  0.82139796,\n", "          0.07027046, -0.5974372 ])},\n", " 'discount': 1.0,\n", " 'is_first': <PERSON><PERSON><PERSON>,\n", " 'is_last': <PERSON><PERSON><PERSON>,\n", " 'is_terminal': <PERSON><PERSON><PERSON>,\n", " 'observation': {'cartesian_position': array([ 0.46371341, -0.06080873,  0.48677155, -2.541821  , -0.30842647,\n", "         -1.1704489 ]),\n", "  'gripper_position': array([0.]),\n", "  'joint_position': array([ 0.33121359, -0.39866462, -0.4616833 , -2.50273585, -0.25965858,\n", "          2.79743648,  1.1308471 ])},\n", " 'reward': 0.0}"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["!zip -r droid_lmdb.zip droid_lmdb"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "biTDUD3fdIph", "outputId": "f362c533-99ed-4ab9-aae1-154f37c3afe2"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  adding: droid_lmdb/ (stored 0%)\n", "  adding: droid_lmdb/1/ (stored 0%)\n", "  adding: droid_lmdb/1/data.mdb (deflated 18%)\n", "  adding: droid_lmdb/0/ (stored 0%)\n", "  adding: droid_lmdb/0/data.mdb (deflated 17%)\n", "  adding: droid_lmdb/2/ (stored 0%)\n", "  adding: droid_lmdb/2/data.mdb (deflated 18%)\n", "  adding: droid_lmdb/split.json (stored 0%)\n"]}]}]}