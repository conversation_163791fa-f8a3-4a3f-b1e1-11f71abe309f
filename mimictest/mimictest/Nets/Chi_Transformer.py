import copy
from typing import Tuple, Sequence, Dict, Union, Optional, Callable
import math
import torch
import torch.nn as nn
from mimictest.Nets.DiffusionRgbEncoder import DiffusionRgbEncoder

class SinusoidalPosEmb(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, x):
        device = x.device
        half_dim = self.dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=device) * -emb)
        emb = x[:, None] * emb[None, :]
        emb = torch.cat((emb.sin(), emb.cos()), dim=-1)
        return emb

class ModuleAttrMixin(nn.Module):
    def __init__(self):
        super().__init__()
        self._dummy_variable = nn.Parameter()

    @property
    def device(self):
        return next(iter(self.parameters())).device

    @property
    def dtype(self):
        return next(iter(self.parameters())).dtype

class TransformerForDiffusion(ModuleAttrMixin):
    def __init__(self,
            input_dim: int,
            output_dim: int,
            max_T: int,
            n_obs_steps,
            cond_dim: int = 0,
            n_layer: int = 12,
            n_head: int = 12,
            n_emb: int = 768,
            p_drop_emb: float = 0.1,
            p_drop_attn: float = 0.1,
            causal_attn: bool=False,
            time_as_cond: bool=True,
            obs_as_cond: bool=False,
            n_cond_layers: int = 0
        ) -> None:
        super().__init__()

        T = max_T
        T_cond = 1
        if not time_as_cond:
            T += 1
            T_cond -= 1
        obs_as_cond = cond_dim > 0
        if obs_as_cond:
            assert time_as_cond
            T_cond += n_obs_steps

        # input embedding stem
        self.input_emb = nn.Linear(input_dim, n_emb)
        self.pos_emb = nn.Parameter(torch.zeros(1, T, n_emb))
        self.drop = nn.Dropout(p_drop_emb)

        # cond encoder
        self.time_emb = SinusoidalPosEmb(n_emb)
        self.cond_obs_emb = None

        if obs_as_cond:
            self.cond_obs_emb = nn.Linear(cond_dim, n_emb)

        self.cond_pos_emb = None
        self.encoder = None
        self.decoder = None
        encoder_only = False
        if T_cond > 0:
            self.cond_pos_emb = nn.Parameter(torch.zeros(1, T_cond, n_emb))
            if n_cond_layers > 0:
                encoder_layer = nn.TransformerEncoderLayer(
                    d_model=n_emb,
                    nhead=n_head,
                    dim_feedforward=4*n_emb,
                    dropout=p_drop_attn,
                    activation='gelu',
                    batch_first=True,
                    norm_first=True
                )
                self.encoder = nn.TransformerEncoder(
                    encoder_layer=encoder_layer,
                    num_layers=n_cond_layers
                )
            else:
                self.encoder = nn.Sequential(
                    nn.Linear(n_emb, 4 * n_emb),
                    nn.Mish(),
                    nn.Linear(4 * n_emb, n_emb)
                )
            # decoder
            decoder_layer = nn.TransformerDecoderLayer(
                d_model=n_emb,
                nhead=n_head,
                dim_feedforward=4*n_emb,
                dropout=p_drop_attn,
                activation='gelu',
                batch_first=True,
                norm_first=True # important for stability
            )
            self.decoder = nn.TransformerDecoder(
                decoder_layer=decoder_layer,
                num_layers=n_layer
            )
        else:
            # encoder only BERT
            encoder_only = True

            encoder_layer = nn.TransformerEncoderLayer(
                d_model=n_emb,
                nhead=n_head,
                dim_feedforward=4*n_emb,
                dropout=p_drop_attn,
                activation='gelu',
                batch_first=True,
                norm_first=True
            )
            self.encoder = nn.TransformerEncoder(
                encoder_layer=encoder_layer,
                num_layers=n_layer
            )

        # attention mask
        if causal_attn:
            # causal mask to ensure that attention is only applied to the left in the input sequence
            # torch.nn.Transformer uses additive mask as opposed to multiplicative mask in minGPT
            # therefore, the upper triangle should be -inf and others (including diag) should be 0.
            sz = T
            mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
            mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
            self.register_buffer("mask", mask)

            if time_as_cond and obs_as_cond:
                S = T_cond
                t, s = torch.meshgrid(
                    torch.arange(T),
                    torch.arange(S),
                    indexing='ij'
                )
                mask = t >= (s-1) # add one dimension since time is the first token in cond
                mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
                self.register_buffer('memory_mask', mask)
            else:
                self.memory_mask = None
        else:
            self.mask = None
            self.memory_mask = None

        # decoder head
        self.ln_f = nn.LayerNorm(n_emb)
        self.head = nn.Linear(n_emb, output_dim)

        # constants
        self.T = T
        self.T_cond = T_cond
        self.time_as_cond = time_as_cond
        self.obs_as_cond = obs_as_cond
        self.encoder_only = encoder_only

        # init
        self.apply(self._init_weights)

    def _init_weights(self, module):
        ignore_types = (nn.Dropout,
            SinusoidalPosEmb,
            nn.TransformerEncoderLayer,
            nn.TransformerDecoderLayer,
            nn.TransformerEncoder,
            nn.TransformerDecoder,
            nn.ModuleList,
            nn.Mish,
            nn.Sequential)
        if isinstance(module, (nn.Linear, nn.Embedding)):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if isinstance(module, nn.Linear) and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.MultiheadAttention):
            weight_names = [
                'in_proj_weight', 'q_proj_weight', 'k_proj_weight', 'v_proj_weight']
            for name in weight_names:
                weight = getattr(module, name)
                if weight is not None:
                    torch.nn.init.normal_(weight, mean=0.0, std=0.02)

            bias_names = ['in_proj_bias', 'bias_k', 'bias_v']
            for name in bias_names:
                bias = getattr(module, name)
                if bias is not None:
                    torch.nn.init.zeros_(bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
        elif isinstance(module, TransformerForDiffusion):
            torch.nn.init.normal_(module.pos_emb, mean=0.0, std=0.02)
            if module.cond_obs_emb is not None:
                torch.nn.init.normal_(module.cond_pos_emb, mean=0.0, std=0.02)
        elif isinstance(module, ignore_types):
            # no param
            pass
        else:
            raise RuntimeError("Unaccounted module {}".format(module))

    def forward(self,
        sample: torch.Tensor,
        timestep: Union[torch.Tensor, float, int],
        cond: Optional[torch.Tensor]=None,
        return_features: bool = False,
        **kwargs):
        """
        x: (B,T,input_dim)
        timestep: (B,) or int, diffusion step
        cond: (B,T',cond_dim)
        return_features: if True, return intermediate features for dispersive loss
        output: (B,T,input_dim) or tuple((B,T,input_dim), features) if return_features=True
        """
        # 1. time
        timesteps = timestep
        if not torch.is_tensor(timesteps):
            # TODO: this requires sync between CPU and GPU. So try to pass timesteps as tensors if you can
            timesteps = torch.tensor([timesteps], dtype=torch.long, device=sample.device)
        elif torch.is_tensor(timesteps) and len(timesteps.shape) == 0:
            timesteps = timesteps[None].to(sample.device)
        # broadcast to batch dimension in a way that's compatible with ONNX/Core ML
        timesteps = timesteps.expand(sample.shape[0])
        time_emb = self.time_emb(timesteps).unsqueeze(1)
        # (B,1,n_emb)

        # process input
        input_emb = self.input_emb(sample)

        if self.encoder_only:
            # BERT
            token_embeddings = torch.cat([time_emb, input_emb], dim=1)
            t = token_embeddings.shape[1]
            position_embeddings = self.pos_emb[
                :, :t, :
            ]  # each position maps to a (learnable) vector
            x = self.drop(token_embeddings + position_embeddings)
            # (B,T+1,n_emb)
            x = self.encoder(src=x, mask=self.mask)
            # (B,T+1,n_emb)
            features = x  # Store features before final processing
            x = x[:,1:,:]
            # (B,T,n_emb)
        else:
            # encoder
            cond_embeddings = time_emb
            if self.obs_as_cond:
                cond_obs_emb = self.cond_obs_emb(cond)
                # (B,To,n_emb)
                cond_embeddings = torch.cat([cond_embeddings, cond_obs_emb], dim=1)
            tc = cond_embeddings.shape[1]
            position_embeddings = self.cond_pos_emb[
                :, :tc, :
            ]  # each position maps to a (learnable) vector
            x = self.drop(cond_embeddings + position_embeddings)
            x = self.encoder(x)
            memory = x
            # (B,T_cond,n_emb)

            # decoder
            token_embeddings = input_emb
            t = token_embeddings.shape[1]
            position_embeddings = self.pos_emb[
                :, :t, :
            ]  # each position maps to a (learnable) vector
            x = self.drop(token_embeddings + position_embeddings)
            # (B,T,n_emb)
            x = self.decoder(
                tgt=x,
                memory=memory,
                tgt_mask=self.mask,
                memory_mask=self.memory_mask
            )
            # (B,T,n_emb)
            features = x  # Store features before final processing

        # head
        x = self.ln_f(x)
        output = self.head(x)
        # (B,T,n_out)

        if return_features:
            return output, features
        return output

class Chi_Transformer(nn.Module):
    def __init__(self,
            camera_num,
            obs_horizon,
            lowdim_obs_dim,
            num_actions,
            vision_backbone,
            pretrained_backbone_weights,
            input_img_shape,
            use_group_norm,
            spatial_softmax_num_keypoints,
            max_T,
            n_layer,
            n_head,
            n_emb,
            p_drop_emb,
            p_drop_attn,
            causal_attn,
            time_as_cond,
            obs_as_cond,
            n_cond_layers,
            use_dispersive_loss=False,
            dispersive_loss_weight=0.01,
        ):

        super().__init__()
        self.use_dispersive_loss = use_dispersive_loss
        self.dispersive_loss_weight = dispersive_loss_weight

        self.vision_encoders = nn.ModuleList([])
        for _ in range(camera_num):
            vision_encoder = DiffusionRgbEncoder(
                vision_backbone,
                pretrained_backbone_weights,
                input_img_shape,
                use_group_norm,
                spatial_softmax_num_keypoints,
            )
            self.vision_encoders.append(vision_encoder)
        vision_feature_dim = self.vision_encoders[0].feature_dim
        obs_dim = vision_feature_dim*camera_num + lowdim_obs_dim
        self.noise_pred_net = TransformerForDiffusion(
            input_dim=num_actions,
            output_dim=num_actions,
            max_T=max_T,
            n_obs_steps=obs_horizon,
            cond_dim=obs_dim,
            n_layer=n_layer,
            n_head=n_head,
            n_emb=n_emb,
            p_drop_emb=p_drop_emb,
            p_drop_attn=p_drop_attn,
            causal_attn=causal_attn,
            time_as_cond=time_as_cond,
            obs_as_cond=obs_as_cond,
            n_cond_layers=n_cond_layers,
        )

    def disp_loss(self, z):
        """
        Dispersive Loss implementation (InfoNCE-L2 variant)

        Args:
            z: Feature tensor of shape (B, ...) where B is batch size

        Returns:
            torch.Tensor: Dispersive loss value
        """
        if z is None:
            return torch.tensor(0.0, device='cpu')

        # Flatten the feature tensor
        z = z.reshape((z.shape[0], -1))
        num_samples = z.shape[0]

        # For larger sample sizes, use sampling strategy for efficiency
        if num_samples > 32:
            sample_size = min(32, num_samples)
            indices = torch.randperm(num_samples, device=z.device)[:sample_size]
            z_sampled = z[indices]
        else:
            z_sampled = z

        # Calculate pairwise L2 distances
        diff = torch.nn.functional.pdist(z_sampled).pow(2) / z_sampled.shape[1]

        # Calculate InfoNCE-style loss
        return torch.log(torch.exp(-diff).mean())

    def forward(self, batch, return_features=False):
        if batch['obs_features'] is None:
            # encoder vision features
            B, T, V, C, H, W = batch['rgb'].shape
            image_features = []
            for view_id in range(V):
                rgb_view = batch['rgb'][:, :, view_id].view(B*T, C, H, W)
                image_features.append(self.vision_encoders[view_id](rgb_view))
            image_features = torch.cat(image_features, dim=1) # (b*t, d)
            image_features = image_features.view(B, T, -1) # (b, t, d)

            # concatenate vision feature and low-dim obs
            obs_features = torch.cat([image_features, batch['low_dim']], dim=-1)
        else:
            obs_features = batch['obs_features']

        # predict the noise residual
        pred_noise = {}
        if return_features or self.use_dispersive_loss:
            out, features = self.noise_pred_net(
                batch['noisy_inputs']['action'],
                batch['timesteps'],
                cond=obs_features,
                return_features=True
            )
            pred_noise['action'] = out
            if return_features:
                return pred_noise, obs_features, features
            return pred_noise, obs_features, features
        else:
            out = self.noise_pred_net(
                batch['noisy_inputs']['action'], batch['timesteps'], cond=obs_features)
            pred_noise['action'] = out
            return pred_noise, obs_features
