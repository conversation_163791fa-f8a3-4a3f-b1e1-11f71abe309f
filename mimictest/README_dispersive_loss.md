# Chi_Transformer with Dispersive Loss

本项目将Dispersive Loss集成到Chi_Transformer中，用于改进扩散策略（Diffusion Policy）的训练效果。

## 🎯 主要特性

- **Dispersive Loss集成**: 在Chi_Transformer中实现了InfoNCE-L2变体的dispersive loss
- **自动特征提取**: TransformerForDiffusion支持返回中间特征用于dispersive loss计算
- **灵活配置**: 可以轻松启用/禁用dispersive loss并调整其权重
- **高效实现**: 对大批次数据使用采样策略提高计算效率

## 🔧 核心修改

### 1. TransformerForDiffusion类
- 添加了`return_features`参数，支持返回中间特征
- 在encoder-only和encoder-decoder模式下都能提取特征

### 2. Chi_Transformer类
- 新增`use_dispersive_loss`和`dispersive_loss_weight`参数
- 实现了`disp_loss()`方法，计算InfoNCE-L2风格的dispersive loss
- 修改`forward()`方法支持特征返回

### 3. DiffusionPolicy类
- 自动检测模型是否启用dispersive loss
- 在损失计算中自动添加dispersive loss项

## 📖 使用方法

### 基础使用

```python
from mimictest.Nets.Chi_Transformer import Chi_Transformer
from mimictest.Wrappers.DiffusionPolicy import DiffusionPolicy

# 创建带有dispersive loss的模型
model = Chi_Transformer(
    camera_num=2,
    obs_horizon=2,
    lowdim_obs_dim=8,
    num_actions=7,
    # ... 其他参数
    use_dispersive_loss=True,      # 启用dispersive loss
    dispersive_loss_weight=0.01,   # 设置权重
)

# 创建DiffusionPolicy
policy = DiffusionPolicy(
    net=model,
    # ... 其他配置
)

# 训练时自动计算dispersive loss
loss_dict = policy.compute_loss(batch)
print(f"Total loss: {loss_dict['total_loss']}")
print(f"Dispersive loss: {loss_dict['dispersive_loss']}")
```

### 配置文件使用

参考`config_dispersive_loss.yaml`文件：

```yaml
model:
  use_dispersive_loss: true
  dispersive_loss_weight: 0.01
```

## 🧮 Dispersive Loss算法

### 核心思想
Dispersive Loss鼓励模型的内部表示在隐藏空间中更加分散，避免表示坍塌，提高模型的表达能力。

### 算法实现
```python
def disp_loss(self, z):
    # 1. 展平特征张量
    z = z.reshape((z.shape[0], -1))
    
    # 2. 对大批次使用采样策略
    if z.shape[0] > 32:
        indices = torch.randperm(z.shape[0])[:32]
        z = z[indices]
    
    # 3. 计算成对L2距离
    diff = torch.nn.functional.pdist(z).pow(2) / z.shape[1]
    
    # 4. 计算InfoNCE风格损失
    return torch.log(torch.exp(-diff).mean())
```

## 📊 参数调优建议

### dispersive_loss_weight
- **0.001-0.005**: 轻微正则化，适合稳定的基线模型
- **0.01-0.02**: 中等强度，推荐的起始值
- **0.05-0.1**: 强正则化，可能影响主要任务性能

### 动态权重调度
```python
# 训练过程中逐渐增加dispersive loss权重
current_weight = start_weight + (end_weight - start_weight) * (epoch / warmup_epochs)
```

## 🔍 监控和调试

### 关键指标
1. **dispersive_loss**: dispersive loss的数值
2. **total_loss**: 总损失（包含dispersive loss）
3. **feature_variance**: 特征方差（衡量分散程度）

### 调试技巧
```python
# 检查特征分散程度
features = model.get_features(batch)
variance = torch.var(features.reshape(features.shape[0], -1), dim=1).mean()
print(f"Feature variance: {variance.item()}")

# 比较有无dispersive loss的效果
model_with_disp = Chi_Transformer(..., use_dispersive_loss=True)
model_without_disp = Chi_Transformer(..., use_dispersive_loss=False)
```

## 🚀 运行示例

```bash
# 运行完整示例
python example_dispersive_loss_usage.py

# 输出包括：
# 1. 训练步骤示例
# 2. 推理示例  
# 3. Dispersive Loss效果分析
```

## 📈 预期效果

使用Dispersive Loss后，你可能观察到：

1. **更好的特征分散性**: 模型特征在隐藏空间中分布更均匀
2. **减少模式坍塌**: 避免模型输出过于相似的结果
3. **提高泛化能力**: 在测试集上表现更稳定
4. **更稳定的训练**: 减少训练过程中的震荡

## ⚠️ 注意事项

1. **计算开销**: Dispersive loss会增加一定的计算成本
2. **权重调优**: 需要根据具体任务调整`dispersive_loss_weight`
3. **批次大小**: 大批次时使用采样策略，可能影响loss的准确性
4. **收敛速度**: 可能会稍微减慢初期收敛速度

## 🔗 相关论文

- Dispersive Loss相关研究
- InfoNCE对比学习方法
- 扩散模型中的正则化技术
