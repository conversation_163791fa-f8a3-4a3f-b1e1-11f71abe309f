[project]
name = "mimictest"
version = "0.1"
dependencies = [
    "torch",
    "torchvision",
    "diffusers",
    "einops",
    "transformers",
    "accelerate",
    "moviepy",
    "wandb",
    "tqdm",
    "stable-baselines3",
    "lmdb",
    "scipy",
]

[project.optional-dependencies]
florence = [
    "timm",
    "flash-attn",
]
pusht = [
    "zarr",
    "gym-pusht",
]

[tool.setuptools]
packages = ["mimictest"]
