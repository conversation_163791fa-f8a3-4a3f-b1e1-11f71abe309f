#!/usr/bin/env python3
"""
示例：如何使用集成了Dispersive Loss的Chi_Transformer

这个示例展示了如何：
1. 创建带有dispersive loss的Chi_Transformer模型
2. 在DiffusionPolicy中使用它
3. 训练时自动计算dispersive loss
"""

import torch
import torch.nn as nn
from mimictest.Nets.Chi_Transformer import Chi_Transformer
from mimictest.Wrappers.DiffusionPolicy import DiffusionPolicy

def create_chi_transformer_with_dispersive_loss():
    """创建带有dispersive loss的Chi_Transformer模型"""
    
    # 模型配置参数
    config = {
        'camera_num': 2,  # 摄像头数量
        'obs_horizon': 2,  # 观察历史长度
        'lowdim_obs_dim': 8,  # 低维观察维度
        'num_actions': 7,  # 动作维度
        'vision_backbone': 'resnet18',  # 视觉骨干网络
        'pretrained_backbone_weights': None,
        'input_img_shape': [3, 224, 224],  # 输入图像形状
        'use_group_norm': True,
        'spatial_softmax_num_keypoints': 32,
        
        # Transformer参数
        'max_T': 16,  # 最大时间步长
        'n_layer': 8,  # Transformer层数
        'n_head': 8,  # 注意力头数
        'n_emb': 512,  # 嵌入维度
        'p_drop_emb': 0.1,  # 嵌入dropout
        'p_drop_attn': 0.1,  # 注意力dropout
        'causal_attn': True,  # 是否使用因果注意力
        'time_as_cond': True,  # 时间作为条件
        'obs_as_cond': True,  # 观察作为条件
        'n_cond_layers': 2,  # 条件编码层数
        
        # Dispersive Loss参数
        'use_dispersive_loss': True,  # 启用dispersive loss
        'dispersive_loss_weight': 0.01,  # dispersive loss权重
    }
    
    # 创建模型
    model = Chi_Transformer(**config)
    
    return model, config

def create_diffusion_policy_with_dispersive_loss():
    """创建使用dispersive loss的DiffusionPolicy"""
    
    # 创建Chi_Transformer模型
    chi_transformer, model_config = create_chi_transformer_with_dispersive_loss()
    
    # DiffusionPolicy配置
    policy_config = {
        'net': chi_transformer,
        'loss_configs': {
            'action': {
                'loss_func': nn.MSELoss(),
                'weight': 1.0,
                'type': 'diffusion',
                'shape': (model_config['max_T'], model_config['num_actions'])
            }
        },
        'prediction_type': 'epsilon',  # 预测噪声
        'scheduler_name': 'ddpm',
        'num_train_steps': 1000,
    }
    
    # 创建DiffusionPolicy
    policy = DiffusionPolicy(**policy_config)
    
    return policy

def example_training_step():
    """示例训练步骤"""
    
    # 创建policy
    policy = create_diffusion_policy_with_dispersive_loss()
    
    # 模拟批次数据
    batch_size = 4
    obs_horizon = 2
    action_horizon = 16
    
    batch = {
        'rgb': torch.randn(batch_size, obs_horizon, 2, 3, 224, 224),  # (B, T, V, C, H, W)
        'low_dim': torch.randn(batch_size, obs_horizon, 8),  # (B, T, D)
        'action': torch.randn(batch_size, action_horizon, 7),  # (B, T, A)
        'obs_features': None,  # 将由模型计算
    }
    
    # 计算损失
    loss_dict = policy.compute_loss(batch)
    
    print("损失组成:")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.item():.6f}")
        else:
            print(f"  {key}: {value}")
    
    return loss_dict

def example_inference():
    """示例推理过程"""
    
    # 创建policy
    policy = create_diffusion_policy_with_dispersive_loss()
    policy.eval()
    
    # 模拟观察数据
    batch_size = 1
    obs_horizon = 2
    
    batch = {
        'rgb': torch.randn(batch_size, obs_horizon, 2, 3, 224, 224),
        'low_dim': torch.randn(batch_size, obs_horizon, 8),
        'obs_features': None,
    }
    
    # 推理
    with torch.no_grad():
        actions = policy.infer(batch)
    
    print(f"推理结果形状: {actions['action'].shape}")
    return actions

def analyze_dispersive_loss_effect():
    """分析dispersive loss的效果"""
    
    print("=== Dispersive Loss效果分析 ===")
    
    # 创建两个模型：一个有dispersive loss，一个没有
    model_with_disp, _ = create_chi_transformer_with_dispersive_loss()
    
    # 创建没有dispersive loss的模型
    config_no_disp = {
        'camera_num': 2, 'obs_horizon': 2, 'lowdim_obs_dim': 8, 'num_actions': 7,
        'vision_backbone': 'resnet18', 'pretrained_backbone_weights': None,
        'input_img_shape': [3, 224, 224], 'use_group_norm': True,
        'spatial_softmax_num_keypoints': 32, 'max_T': 16, 'n_layer': 8,
        'n_head': 8, 'n_emb': 512, 'p_drop_emb': 0.1, 'p_drop_attn': 0.1,
        'causal_attn': True, 'time_as_cond': True, 'obs_as_cond': True,
        'n_cond_layers': 2, 'use_dispersive_loss': False, 'dispersive_loss_weight': 0.0,
    }
    model_no_disp = Chi_Transformer(**config_no_disp)
    
    # 创建测试数据
    batch = {
        'rgb': torch.randn(4, 2, 2, 3, 224, 224),
        'low_dim': torch.randn(4, 2, 8),
        'obs_features': None,
        'noisy_inputs': {'action': torch.randn(4, 16, 7)},
        'timesteps': torch.randint(1, 1000, (4,)),
    }
    
    # 测试有dispersive loss的模型
    model_with_disp.train()
    pred_with, _, features_with = model_with_disp(batch, return_features=True)
    disp_loss_value = model_with_disp.disp_loss(features_with)
    
    print(f"带Dispersive Loss的模型:")
    print(f"  特征形状: {features_with.shape}")
    print(f"  Dispersive Loss值: {disp_loss_value.item():.6f}")
    
    # 测试没有dispersive loss的模型
    model_no_disp.train()
    pred_no, _, features_no = model_no_disp(batch, return_features=True)
    
    print(f"不带Dispersive Loss的模型:")
    print(f"  特征形状: {features_no.shape}")
    
    # 比较特征分布
    with torch.no_grad():
        # 计算特征的方差（衡量分散程度）
        var_with = torch.var(features_with.reshape(features_with.shape[0], -1), dim=1).mean()
        var_no = torch.var(features_no.reshape(features_no.shape[0], -1), dim=1).mean()
        
        print(f"特征方差比较:")
        print(f"  带Dispersive Loss: {var_with.item():.6f}")
        print(f"  不带Dispersive Loss: {var_no.item():.6f}")

if __name__ == "__main__":
    print("=== Chi_Transformer with Dispersive Loss 示例 ===\n")
    
    # 1. 训练示例
    print("1. 训练步骤示例:")
    example_training_step()
    print()
    
    # 2. 推理示例
    print("2. 推理示例:")
    example_inference()
    print()
    
    # 3. 分析dispersive loss效果
    print("3. Dispersive Loss效果分析:")
    analyze_dispersive_loss_effect()
    print()
    
    print("=== 使用说明 ===")
    print("1. 在创建Chi_Transformer时设置 use_dispersive_loss=True")
    print("2. 调整 dispersive_loss_weight 来控制dispersive loss的影响")
    print("3. DiffusionPolicy会自动处理dispersive loss的计算和添加")
    print("4. 训练时可以监控 'dispersive_loss' 项来观察效果")
