{"algo_name": "iris", "experiment": {"name": "test", "validate": false, "logging": {"terminal_output_to_txt": true, "log_tb": true, "log_wandb": false, "wandb_proj_name": "debug"}, "save": {"enabled": true, "every_n_seconds": null, "every_n_epochs": 50, "epochs": [], "on_best_validation": false, "on_best_rollout_return": false, "on_best_rollout_success_rate": true}, "epoch_every_n_steps": 100, "validation_epoch_every_n_steps": 10, "env": null, "additional_envs": null, "render": false, "render_video": true, "keep_all_videos": false, "video_skip": 5, "rollout": {"enabled": true, "n": 50, "horizon": 400, "rate": 50, "warmstart": 0, "terminate_on_success": true}, "env_meta_update_dict": {}, "ckpt_path": null}, "train": {"data": null, "output_dir": "../iris_trained_models", "normalize_weights_by_ds_size": false, "num_data_workers": 0, "hdf5_cache_mode": "all", "hdf5_use_swmr": true, "hdf5_load_next_obs": true, "hdf5_normalize_obs": false, "hdf5_filter_key": null, "hdf5_validation_filter_key": null, "seq_length": 10, "pad_seq_length": true, "frame_stack": 1, "pad_frame_stack": true, "dataset_keys": ["actions", "rewards", "dones"], "action_keys": ["actions"], "action_config": {"actions": {"normalization": null}}, "goal_mode": null, "cuda": true, "batch_size": 100, "num_epochs": 2000, "seed": 1, "max_grad_norm": null}, "algo": {"mode": "separate", "actor_use_random_subgoals": false, "subgoal_update_interval": 10, "latent_subgoal": {"enabled": false, "prior_correction": {"enabled": false, "num_samples": 100}}, "value_planner": {"planner": {"optim_params": {"goal_network": {"learning_rate": {"initial": 0.0001, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}}}, "subgoal_horizon": 10, "ae": {"planner_layer_dims": [300, 400]}, "vae": {"enabled": true, "latent_dim": 16, "latent_clip": null, "kl_weight": 1.0, "decoder": {"is_conditioned": true, "reconstruction_sum_across_elements": false}, "prior": {"learn": false, "is_conditioned": false, "use_gmm": false, "gmm_num_modes": 10, "gmm_learn_weights": false, "use_categorical": false, "categorical_dim": 10, "categorical_gumbel_softmax_hard": false, "categorical_init_temp": 1.0, "categorical_temp_anneal_step": 0.001, "categorical_min_temp": 0.3}, "encoder_layer_dims": [300, 400], "decoder_layer_dims": [300, 400], "prior_layer_dims": [300, 400]}}, "value": {"optim_params": {"critic": {"learning_rate": {"initial": 0.001, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}, "start_epoch": -1, "end_epoch": -1}, "action_sampler": {"learning_rate": {"initial": 0.001, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}, "start_epoch": -1, "end_epoch": -1}, "actor": {"learning_rate": {"initial": 0.001, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}, "start_epoch": -1, "end_epoch": -1}}, "discount": 0.99, "n_step": 1, "target_tau": 0.005, "infinite_horizon": false, "critic": {"use_huber": false, "max_gradient_norm": null, "value_bounds": null, "num_action_samples": 10, "num_action_samples_rollout": 100, "ensemble": {"n": 2, "weight": 0.75}, "distributional": {"enabled": false, "num_atoms": 51}, "layer_dims": [300, 400]}, "action_sampler": {"actor_layer_dims": [1024, 1024], "gmm": {"enabled": false, "num_modes": 5, "min_std": 0.0001, "std_activation": "softplus", "low_noise_eval": true}, "vae": {"enabled": true, "latent_dim": 14, "latent_clip": null, "kl_weight": 1.0, "decoder": {"is_conditioned": true, "reconstruction_sum_across_elements": false}, "prior": {"learn": false, "is_conditioned": false, "use_gmm": false, "gmm_num_modes": 10, "gmm_learn_weights": false, "use_categorical": false, "categorical_dim": 10, "categorical_gumbel_softmax_hard": false, "categorical_init_temp": 1.0, "categorical_temp_anneal_step": 0.001, "categorical_min_temp": 0.3}, "encoder_layer_dims": [300, 400], "decoder_layer_dims": [300, 400], "prior_layer_dims": [300, 400]}, "freeze_encoder_epoch": -1}, "actor": {"enabled": false, "perturbation_scale": 0.05, "layer_dims": [300, 400]}}, "num_samples": 100}, "actor": {"optim_params": {"policy": {"optimizer_type": "adam", "learning_rate": {"initial": 0.0001, "decay_factor": 0.1, "epoch_schedule": [], "scheduler_type": "multistep"}, "regularization": {"L2": 0.0}}}, "loss": {"l2_weight": 1.0, "l1_weight": 0.0, "cos_weight": 0.0}, "actor_layer_dims": [1024, 1024], "rnn": {"enabled": true, "horizon": 10, "hidden_dim": 400, "rnn_type": "LSTM", "num_layers": 2, "open_loop": false, "kwargs": {"bidirectional": false}}, "transformer": {"enabled": false, "context_length": 10, "embed_dim": 512, "num_layers": 6, "num_heads": 8, "emb_dropout": 0.1, "attn_dropout": 0.1, "block_output_dropout": 0.1, "sinusoidal_embedding": false, "activation": "gelu", "supervise_all_steps": false, "nn_parameter_for_timesteps": true, "pred_future_acs": false}}}, "observation": {"value_planner": {"planner": {"modalities": {"obs": {"low_dim": ["robot0_eef_pos", "robot0_eef_quat", "robot0_gripper_qpos", "object"], "rgb": [], "depth": [], "scan": []}, "goal": {"low_dim": [], "rgb": [], "depth": [], "scan": []}, "subgoal": {"low_dim": ["robot0_eef_pos", "robot0_eef_quat", "robot0_gripper_qpos", "object"], "rgb": [], "depth": [], "scan": []}}, "encoder": {"low_dim": {"core_class": null, "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "rgb": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "depth": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "scan": {"core_class": "ScanCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}}}, "value": {"modalities": {"obs": {"low_dim": ["robot0_eef_pos", "robot0_eef_quat", "robot0_gripper_qpos", "object"], "rgb": [], "depth": [], "scan": []}, "goal": {"low_dim": [], "rgb": [], "depth": [], "scan": []}}, "encoder": {"low_dim": {"core_class": null, "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "rgb": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "depth": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "scan": {"core_class": "ScanCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}}}}, "actor": {"modalities": {"obs": {"low_dim": ["robot0_eef_pos", "robot0_eef_quat", "robot0_gripper_qpos", "object"], "rgb": [], "depth": [], "scan": []}, "goal": {"low_dim": [], "rgb": [], "depth": [], "scan": []}}, "encoder": {"low_dim": {"core_class": null, "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "rgb": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "depth": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "scan": {"core_class": "ScanCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}}}}, "meta": {"hp_base_config_file": null, "hp_keys": [], "hp_values": []}}