{"algo_name": "bc", "experiment": {"name": "test", "validate": true, "logging": {"terminal_output_to_txt": true, "log_tb": true, "log_wandb": false, "wandb_proj_name": "debug"}, "mse": {}, "save": {"enabled": true, "every_n_seconds": null, "every_n_epochs": 50, "epochs": [], "on_best_validation": false, "on_best_rollout_return": false, "on_best_rollout_success_rate": true}, "epoch_every_n_steps": 100, "validation_epoch_every_n_steps": 10, "env": null, "additional_envs": null, "render": false, "render_video": true, "keep_all_videos": false, "video_skip": 5, "rollout": {"enabled": true, "n": 50, "horizon": 400, "rate": 50, "warmstart": 0, "terminate_on_success": true}}, "train": {"data": null, "output_dir": "../bc_transformer_trained_models", "num_data_workers": 0, "hdf5_cache_mode": "low_dim", "hdf5_use_swmr": true, "hdf5_load_next_obs": false, "hdf5_normalize_obs": false, "hdf5_filter_key": null, "seq_length": 1, "pad_seq_length": true, "frame_stack": 10, "pad_frame_stack": true, "dataset_keys": ["actions"], "goal_mode": null, "cuda": true, "batch_size": 100, "num_epochs": 2000, "seed": 1}, "algo": {"optim_params": {"policy": {"optimizer_type": "adamw", "learning_rate": {"initial": 0.0001, "decay_factor": 0.1, "epoch_schedule": [100], "scheduler_type": "linear"}, "regularization": {"L2": 0.01}}}, "loss": {"l2_weight": 1.0, "l1_weight": 0.0, "cos_weight": 0.0}, "actor_layer_dims": [], "gaussian": {"enabled": false}, "gmm": {"enabled": true, "num_modes": 5, "min_std": 0.0001, "std_activation": "softplus", "low_noise_eval": true}, "vae": {"enabled": false}, "rnn": {"enabled": false}, "transformer": {"enabled": true, "supervise_all_steps": false, "num_layers": 6, "embed_dim": 512, "num_heads": 8}}, "observation": {"modalities": {"obs": {"low_dim": ["robot0_eef_pos", "robot0_eef_quat", "robot0_gripper_qpos", "object"], "rgb": [], "depth": [], "scan": []}, "goal": {"low_dim": [], "rgb": [], "depth": [], "scan": []}}, "encoder": {"low_dim": {"core_class": null, "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "rgb": {"core_class": "VisualCore", "core_kwargs": {"feature_dimension": 64, "backbone_class": "ResNet18Conv", "backbone_kwargs": {"pretrained": false, "input_coord_conv": false}, "pool_class": "SpatialSoftmax", "pool_kwargs": {"num_kp": 32, "learnable_temperature": false, "temperature": 1.0, "noise_std": 0.0}}, "obs_randomizer_class": "CropRandomizer", "obs_randomizer_kwargs": {"crop_height": 76, "crop_width": 76, "num_crops": 1, "pos_enc": false}}, "depth": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "scan": {"core_class": "ScanCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}}}}