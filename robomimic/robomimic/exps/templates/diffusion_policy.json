{"algo_name": "diffusion_policy", "experiment": {"name": "test", "validate": false, "logging": {"terminal_output_to_txt": true, "log_tb": true, "log_wandb": false, "wandb_proj_name": "debug"}, "save": {"enabled": true, "every_n_seconds": null, "every_n_epochs": 50, "epochs": [], "on_best_validation": false, "on_best_rollout_return": false, "on_best_rollout_success_rate": true}, "epoch_every_n_steps": 100, "validation_epoch_every_n_steps": 10, "env": null, "additional_envs": null, "render": false, "render_video": true, "keep_all_videos": false, "video_skip": 5, "rollout": {"enabled": true, "n": 50, "horizon": 400, "rate": 50, "warmstart": 0, "terminate_on_success": true}, "env_meta_update_dict": {}, "ckpt_path": null}, "train": {"data": null, "output_dir": "../diffusion_policy_trained_models", "normalize_weights_by_ds_size": false, "num_data_workers": 0, "hdf5_cache_mode": "all", "hdf5_use_swmr": true, "hdf5_load_next_obs": false, "hdf5_normalize_obs": false, "hdf5_filter_key": null, "hdf5_validation_filter_key": null, "seq_length": 16, "pad_seq_length": true, "frame_stack": 2, "pad_frame_stack": true, "dataset_keys": ["actions", "rewards", "dones"], "action_keys": ["actions"], "action_config": {"actions": {"normalization": null}}, "goal_mode": null, "cuda": true, "batch_size": 100, "num_epochs": 2000, "seed": 1, "max_grad_norm": null}, "algo": {"optim_params": {"policy": {"optimizer_type": "adamw", "learning_rate": {"initial": 0.0001, "decay_factor": 0.1, "step_every_batch": true, "scheduler_type": "cosine", "num_cycles": 0.5, "warmup_steps": 500, "epoch_schedule": []}, "regularization": {"L2": 1e-06}}}, "horizon": {"observation_horizon": 2, "action_horizon": 8, "prediction_horizon": 16}, "unet": {"enabled": true, "diffusion_step_embed_dim": 256, "down_dims": [256, 512, 1024], "kernel_size": 5, "n_groups": 8}, "ema": {"enabled": true, "power": 0.75}, "ddpm": {"enabled": true, "num_train_timesteps": 100, "num_inference_timesteps": 100, "beta_schedule": "squaredcos_cap_v2", "clip_sample": true, "prediction_type": "epsilon"}, "ddim": {"enabled": false, "num_train_timesteps": 100, "num_inference_timesteps": 10, "beta_schedule": "squaredcos_cap_v2", "clip_sample": true, "set_alpha_to_one": true, "steps_offset": 0, "prediction_type": "epsilon"}}, "observation": {"modalities": {"obs": {"low_dim": ["robot0_eef_pos", "robot0_eef_quat", "robot0_gripper_qpos", "object"], "rgb": [], "depth": [], "scan": []}, "goal": {"low_dim": [], "rgb": [], "depth": [], "scan": []}}, "encoder": {"low_dim": {"core_class": null, "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "rgb": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "depth": {"core_class": "VisualCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "scan": {"core_class": "ScanCore", "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}}}, "meta": {"hp_base_config_file": null, "hp_keys": [], "hp_values": []}}