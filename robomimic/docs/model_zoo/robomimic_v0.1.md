# robomimic-v0.1

We provide links below to several pretrained models that were trained with robomimic-v0.1 for our CoRL 2021 study. All success rates listed below are approximate - they may vary.

<div class="admonition note">
<p class="admonition-title">Note: see tutorial on using these models</p>

See the ["Using Pretrained Models"](../tutorials/using_pretrained_models.html) tutorial for instructions on using these models.

</div>

<div class="admonition warning">
<p class="admonition-title">Warning: use correct robomimic and robosuite versions!</p>

When using these trained models, please make sure that robosuite is on the [`offline_study` branch](https://github.com/ARISE-Initiative/robosuite/tree/offline_study), and that robomimic is on the [`v0.1` branch](https://github.com/ARISE-Initiative/robomimic/tree/v0.1).

</div>

<div class="admonition note">
<p class="admonition-title">Troubleshooting download issues</p>

Some users (eg. using Chrome) may experience issues with the dataset download links. If you are experiencing these issues, follow these steps: (1) right click on the download link, (2) select the option to copy the linked url, and (3) copy paste the link in a new tab.

</div>

## Proficient-Human (PH)

### Lift (PH)

<img src="../images/lift.png" alt="proficient_human" style="zoom:33%;" />

#### BC-RNN

- low-dim agent (success rate ~100%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/lift/bc_rnn/lift_ph_low_dim_epoch_1000_succ_100.pth))
- image agent (success rate ~100%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/lift/bc_rnn/lift_ph_image_epoch_500_succ_100.pth))

### Can (PH)

<img src="../images/can.png" alt="proficient_human" style="zoom:33%;" />

#### BC-RNN

- low-dim agent (success rate ~100%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/can/bc_rnn/can_ph_low_dim_epoch_1150_succ_100.pth))

- image agent (success rate ~100%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/can/bc_rnn/can_ph_image_epoch_300_succ_100.pth))

  

### Square (PH)

<img src="../images/square.png" alt="proficient_human" style="zoom:33%;" />

#### BC-RNN

- low-dim agent (success rate ~84%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/square/bc_rnn/square_ph_low_dim_epoch_1850_succ_84.pth))

- image agent (success rate ~78%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/square/bc_rnn/square_ph_image_epoch_540_succ_78.pth))

  

### Transport (PH)

<img src="../images/transport.png" alt="proficient_human" style="zoom:33%;" />

#### BC-RNN

- low-dim agent (success rate ~78%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/transport/bc_rnn/transport_ph_low_dim_epoch_1000_succ_78.pth))

- image agent (success rate ~70%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/transport/bc_rnn/transport_ph_image_epoch_580_succ_70.pth))

  

### Tool Hang (PH)

<img src="../images/tool_hang.png" alt="proficient_human" style="zoom:33%;" />

#### BC-RNN

- low-dim agent (success rate ~14%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/tool_hang/bc_rnn/tool_hang_ph_low_dim_epoch_2000_succ_14.pth))
- image agent (success rate ~ 50-74%) ([download](http://downloads.cs.stanford.edu/downloads/rt_benchmark/model_zoo/tool_hang/bc_rnn/tool_hang_ph_image_epoch_440_succ_74.pth))

