# Robomimic 中 Diffusion Policy 实现分析

## 概述

Diffusion Policy 是在 robomimic v0.5.0 版本中引入的一种新的模仿学习算法，基于 [Diffusion Policy: Visuomotor Policy Learning via Action Diffusion](https://arxiv.org/pdf/2303.04137v5) 论文实现。该实现由 Cheng Chi 贡献到 robomimic 代码库中。

## 核心组件结构

### 1. 算法核心实现 (`algo/diffusion_policy.py`)

#### `DiffusionPolicyUNet` 类
这是 Diffusion Policy 的主要算法类，继承自 `PolicyAlgo`。

**关键特性：**
- 使用 UNet 架构进行噪声预测
- 支持 DDPM 和 DDIM 两种噪声调度器
- 集成 EMA (Exponential Moving Average) 机制
- 支持观测队列和动作队列进行推理

**核心方法：**

```python
class DiffusionPolicyUNet(PolicyAlgo):
    def _create_networks(self):
        """创建网络架构"""
        # 观测编码器
        obs_encoder = ObsNets.ObservationGroupEncoder(...)
        # 替换 BatchNorm 为 GroupNorm (EMA 兼容性)
        obs_encoder = replace_bn_with_gn(obs_encoder)
        
        # 噪声预测网络 (UNet)
        noise_pred_net = DPNets.ConditionalUnet1D(
            input_dim=self.ac_dim,
            global_cond_dim=obs_dim * observation_horizon
        )
        
        # 噪声调度器 (DDPM 或 DDIM)
        noise_scheduler = DDPMScheduler(...) or DDIMScheduler(...)
        
        # EMA 模型
        ema = EMAModel(model=nets, power=self.algo_config.ema.power)
    
    def train_on_batch(self, batch, epoch, validate=False):
        """训练步骤"""
        # 1. 编码观测
        obs_features = self.nets["policy"]["obs_encoder"](batch["obs"])
        obs_cond = obs_features.flatten(start_dim=1)
        
        # 2. 添加噪声到动作 (前向扩散过程)
        noise = torch.randn(actions.shape, device=self.device)
        timesteps = torch.randint(0, num_train_timesteps, (B,))
        noisy_actions = self.noise_scheduler.add_noise(actions, noise, timesteps)
        
        # 3. 预测噪声
        noise_pred = self.nets["policy"]["noise_pred_net"](
            noisy_actions, timesteps, global_cond=obs_cond
        )
        
        # 4. L2 损失
        loss = F.mse_loss(noise_pred, noise)
        
        # 5. 更新网络和 EMA
        TorchUtils.backprop_for_loss(...)
        if self.ema is not None:
            self.ema.step(self.nets)
    
    def get_action(self, obs_dict, goal_dict=None):
        """推理阶段获取动作"""
        # 使用动作队列机制
        if len(self.action_queue) == 0:
            action_sequence = self._get_action_trajectory(obs_dict)
            self.action_queue.extend(action_sequence[0])
        
        return self.action_queue.popleft()
    
    def _get_action_trajectory(self, obs_dict, goal_dict=None):
        """反向扩散过程生成动作轨迹"""
        # 1. 编码观测
        obs_features = nets["policy"]["obs_encoder"](obs_dict)
        obs_cond = obs_features.flatten(start_dim=1)
        
        # 2. 从高斯噪声开始
        noisy_action = torch.randn((B, Tp, action_dim), device=self.device)
        
        # 3. 反向扩散步骤
        self.noise_scheduler.set_timesteps(num_inference_timesteps)
        for k in self.noise_scheduler.timesteps:
            # 预测噪声
            noise_pred = nets["policy"]["noise_pred_net"](
                sample=noisy_action, timestep=k, global_cond=obs_cond
            )
            # 去噪步骤
            noisy_action = self.noise_scheduler.step(
                model_output=noise_pred, timestep=k, sample=noisy_action
            ).prev_sample
        
        return noisy_action
```

### 2. 网络架构 (`models/diffusion_policy_nets.py`)

#### `ConditionalUnet1D` 类
这是 Diffusion Policy 的核心网络架构，专为 1D 动作序列设计。

**架构特点：**
- **输入**: 噪声动作序列 + 时间步嵌入 + 观测条件
- **输出**: 预测的噪声
- **结构**: U-Net 式的编码器-解码器架构

**关键组件：**

```python
class ConditionalUnet1D(nn.Module):
    def __init__(self, 
        input_dim,              # 动作维度
        global_cond_dim,        # 全局条件维度 (obs_horizon * obs_dim)
        diffusion_step_embed_dim=256,  # 扩散步骤嵌入维度
        down_dims=[256,512,1024],      # UNet 层级维度
        kernel_size=5,          # 卷积核大小
        n_groups=8             # GroupNorm 组数
    ):
        # 扩散步骤编码器 (正弦位置编码)
        self.diffusion_step_encoder = nn.Sequential(
            SinusoidalPosEmb(diffusion_step_embed_dim),
            nn.Linear(diffusion_step_embed_dim, diffusion_step_embed_dim * 4),
            nn.Mish(),
            nn.Linear(diffusion_step_embed_dim * 4, diffusion_step_embed_dim),
        )
        
        # 下采样模块
        self.down_modules = nn.ModuleList([
            ConditionalResidualBlock1D(...),
            ConditionalResidualBlock1D(...),
            Downsample1d(...)
        ])
        
        # 中间模块
        self.mid_modules = nn.ModuleList([
            ConditionalResidualBlock1D(...),
            ConditionalResidualBlock1D(...)
        ])
        
        # 上采样模块
        self.up_modules = nn.ModuleList([
            ConditionalResidualBlock1D(...),
            ConditionalResidualBlock1D(...),
            Upsample1d(...)
        ])
        
        # 最终卷积层
        self.final_conv = nn.Sequential(
            Conv1dBlock(...),
            nn.Conv1d(start_dim, input_dim, 1)
        )
```

#### `ConditionalResidualBlock1D` 类
使用 FiLM (Feature-wise Linear Modulation) 机制的条件残差块：

```python
class ConditionalResidualBlock1D(nn.Module):
    def forward(self, x, cond):
        """
        x: [batch_size, in_channels, horizon] - 输入特征
        cond: [batch_size, cond_dim] - 条件信息 (时间步 + 观测)
        """
        out = self.blocks[0](x)
        
        # FiLM 调制
        embed = self.cond_encoder(cond)
        embed = embed.reshape(embed.shape[0], 2, self.out_channels, 1)
        scale = embed[:,0,...]  # 缩放因子
        bias = embed[:,1,...]   # 偏置
        out = scale * out + bias
        
        out = self.blocks[1](out)
        out = out + self.residual_conv(x)  # 残差连接
        return out
```

### 3. 配置系统 (`config/diffusion_policy_config.py`)

#### `DiffusionPolicyConfig` 类
继承自 `BaseConfig`，提供 Diffusion Policy 的默认配置：

```python
class DiffusionPolicyConfig(BaseConfig):
    ALGO_NAME = "diffusion_policy"
    
    def train_config(self):
        # 数据加载配置
        self.train.hdf5_load_next_obs = False  # 不需要下一个观测
        self.train.seq_length = 16            # 序列长度
        self.train.frame_stack = 2            # 帧堆叠
    
    def algo_config(self):
        # 优化器配置
        self.algo.optim_params.policy.optimizer_type = "adamw"
        self.algo.optim_params.policy.learning_rate.initial = 1e-4
        self.algo.optim_params.policy.learning_rate.scheduler_type = "cosine"
        
        # 时间窗口配置
        self.algo.horizon.observation_horizon = 2   # 观测窗口
        self.algo.horizon.action_horizon = 8        # 动作窗口
        self.algo.horizon.prediction_horizon = 16   # 预测窗口
        
        # UNet 配置
        self.algo.unet.enabled = True
        self.algo.unet.diffusion_step_embed_dim = 256
        self.algo.unet.down_dims = [256,512,1024]
        
        # EMA 配置
        self.algo.ema.enabled = True
        self.algo.ema.power = 0.75
        
        # DDPM 噪声调度器配置
        self.algo.ddpm.enabled = True
        self.algo.ddpm.num_train_timesteps = 100
        self.algo.ddpm.num_inference_timesteps = 100
        self.algo.ddpm.beta_schedule = 'squaredcos_cap_v2'
        self.algo.ddpm.prediction_type = 'epsilon'
```

## 关键设计决策

### 1. 时间窗口机制
Diffusion Policy 使用三个关键的时间窗口参数：

- **`observation_horizon` (To)**: 观测窗口，通常为 2
- **`action_horizon` (Ta)**: 执行窗口，从预测的动作中取前几个执行
- **`prediction_horizon` (Tp)**: 预测窗口，总预测动作数量

### 2. 噪声调度策略
支持两种噪声调度器：

- **DDPM**: 更慢但质量更高
- **DDIM**: 更快的推理速度

### 3. 动作归一化要求
- 所有动作必须归一化到 `[-1, 1]` 范围
- 需要在数据预处理时启用 `hdf5_normalize_action`

### 4. 网络稳定性优化
- 使用 GroupNorm 替代 BatchNorm 以兼容 EMA
- EMA 机制提高训练稳定性和推理性能

## 使用方式

### 1. 基本训练
```bash
python train.py --config ../exps/templates/diffusion_policy.json --dataset /path/to/dataset.hdf5
```

### 2. 自定义配置
```python
# 在 hyperparam_helper.py 中
config.algo_name = "diffusion_policy"

# 调整时间窗口
config.algo.horizon.observation_horizon = 2
config.algo.horizon.action_horizon = 8
config.algo.horizon.prediction_horizon = 16

# 选择噪声调度器
config.algo.ddpm.enabled = True
config.algo.ddim.enabled = False
```

## 技术要点

### 1. 扩散过程
- **前向过程**: 在训练时向动作添加噪声
- **反向过程**: 在推理时通过去噪生成动作

### 2. 条件生成
- 使用观测特征作为全局条件
- 通过 FiLM 机制将条件信息注入网络

### 3. 队列机制
- 观测队列管理历史观测
- 动作队列实现流畅的动作执行

### 4. EMA 稳定性
- 维护模型权重的指数移动平均
- 推理时使用 EMA 模型而非原始模型

## 依赖关系

### 外部依赖
- `diffusers==0.11.1`: 提供 DDPM/DDIM 调度器
- PyTorch: 核心深度学习框架

### 内部模块
- `robomimic.models.obs_nets`: 观测编码器
- `robomimic.algo`: 算法基类
- `robomimic.utils.*`: 各种工具函数

## 文件结构总览

```
robomimic/
├── algo/
│   └── diffusion_policy.py           # 主算法实现
├── models/
│   └── diffusion_policy_nets.py      # 网络架构
├── config/
│   └── diffusion_policy_config.py    # 配置类
├── exps/templates/
│   └── diffusion_policy.json         # 默认配置模板
└── docs/tutorials/
    └── training_diffusion_policy.md  # 训练教程
```

## 总结

Robomimic 中的 Diffusion Policy 实现是一个完整且经过优化的模仿学习解决方案。它不仅实现了原论文的核心思想，还针对实际应用做了多项工程优化，包括队列机制、EMA 稳定性、多种噪声调度器支持等。该实现为研究者和从业者提供了一个高质量的起点，可以在此基础上进行进一步的研究和应用开发。