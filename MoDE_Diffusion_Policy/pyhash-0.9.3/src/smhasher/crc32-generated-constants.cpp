#define POLY 0x82f63b78
#define LONG_SHIFT 8192
#define SHORT_SHIFT 256

static uint32_t table[16][256] =
{
    { 0x00000000, 0xf26b8303, 0xe13b70f7, 0x1350f3f4, 0xc79a971f, 0x35f1141c, 0x26a1e7e8, 0xd4ca64eb, 0x8ad958cf, 0x78b2dbcc, 0x6be22838, 0x9989ab3b, 0x4d43cfd0, 0xbf284cd3, 0xac78bf27, 0x5e133c24, 0x105ec76f, 0xe235446c, 0xf165b798, 0x030e349b, 0xd7c45070, 0x25afd373, 0x36ff2087, 0xc494a384, 0x9a879fa0, 0x68ec1ca3, 0x7bbcef57, 0x89d76c54, 0x5d1d08bf, 0xaf768bbc, 0xbc267848, 0x4e4dfb4b, 0x20bd8ede, 0xd2d60ddd, 0xc186fe29, 0x33ed7d2a, 0xe72719c1, 0x154c9ac2, 0x061c6936, 0xf477ea35, 0xaa64d611, 0x580f5512, 0x4b5fa6e6, 0xb93425e5, 0x6dfe410e, 0x9f95c20d, 0x8cc531f9, 0x7eaeb2fa, 0x30e349b1, 0xc288cab2, 0xd1d83946, 0x23b3ba45, 0xf779deae, 0x05125dad, 0x1642ae59, 0xe4292d5a, 0xba3a117e, 0x4851927d, 0x5b016189, 0xa96ae28a, 0x7da08661, 0x8fcb0562, 0x9c9bf696, 0x6ef07595, 0x417b1dbc, 0xb3109ebf, 0xa0406d4b, 0x522bee48, 0x86e18aa3, 0x748a09a0, 0x67dafa54, 0x95b17957, 0xcba24573, 0x39c9c670, 0x2a993584, 0xd8f2b687, 0x0c38d26c, 0xfe53516f, 0xed03a29b, 0x1f682198, 0x5125dad3, 0xa34e59d0, 0xb01eaa24, 0x42752927, 0x96bf4dcc, 0x64d4cecf, 0x77843d3b, 0x85efbe38, 0xdbfc821c, 0x2997011f, 0x3ac7f2eb, 0xc8ac71e8, 0x1c661503, 0xee0d9600, 0xfd5d65f4, 0x0f36e6f7, 0x61c69362, 0x93ad1061, 0x80fde395, 0x72966096, 0xa65c047d, 0x5437877e, 0x4767748a, 0xb50cf789, 0xeb1fcbad, 0x197448ae, 0x0a24bb5a, 0xf84f3859, 0x2c855cb2, 0xdeeedfb1, 0xcdbe2c45, 0x3fd5af46, 0x7198540d, 0x83f3d70e, 0x90a324fa, 0x62c8a7f9, 0xb602c312, 0x44694011, 0x5739b3e5, 0xa55230e6, 0xfb410cc2, 0x092a8fc1, 0x1a7a7c35, 0xe811ff36, 0x3cdb9bdd, 0xceb018de, 0xdde0eb2a, 0x2f8b6829, 0x82f63b78, 0x709db87b, 0x63cd4b8f, 0x91a6c88c, 0x456cac67, 0xb7072f64, 0xa457dc90, 0x563c5f93, 0x082f63b7, 0xfa44e0b4, 0xe9141340, 0x1b7f9043, 0xcfb5f4a8, 0x3dde77ab, 0x2e8e845f, 0xdce5075c, 0x92a8fc17, 0x60c37f14, 0x73938ce0, 0x81f80fe3, 0x55326b08, 0xa759e80b, 0xb4091bff, 0x466298fc, 0x1871a4d8, 0xea1a27db, 0xf94ad42f, 0x0b21572c, 0xdfeb33c7, 0x2d80b0c4, 0x3ed04330, 0xccbbc033, 0xa24bb5a6, 0x502036a5, 0x4370c551, 0xb11b4652, 0x65d122b9, 0x97baa1ba, 0x84ea524e, 0x7681d14d, 0x2892ed69, 0xdaf96e6a, 0xc9a99d9e, 0x3bc21e9d, 0xef087a76, 0x1d63f975, 0x0e330a81, 0xfc588982, 0xb21572c9, 0x407ef1ca, 0x532e023e, 0xa145813d, 0x758fe5d6, 0x87e466d5, 0x94b49521, 0x66df1622, 0x38cc2a06, 0xcaa7a905, 0xd9f75af1, 0x2b9cd9f2, 0xff56bd19, 0x0d3d3e1a, 0x1e6dcdee, 0xec064eed, 0xc38d26c4, 0x31e6a5c7, 0x22b65633, 0xd0ddd530, 0x0417b1db, 0xf67c32d8, 0xe52cc12c, 0x1747422f, 0x49547e0b, 0xbb3ffd08, 0xa86f0efc, 0x5a048dff, 0x8ecee914, 0x7ca56a17, 0x6ff599e3, 0x9d9e1ae0, 0xd3d3e1ab, 0x21b862a8, 0x32e8915c, 0xc083125f, 0x144976b4, 0xe622f5b7, 0xf5720643, 0x07198540, 0x590ab964, 0xab613a67, 0xb831c993, 0x4a5a4a90, 0x9e902e7b, 0x6cfbad78, 0x7fab5e8c, 0x8dc0dd8f, 0xe330a81a, 0x115b2b19, 0x020bd8ed, 0xf0605bee, 0x24aa3f05, 0xd6c1bc06, 0xc5914ff2, 0x37faccf1, 0x69e9f0d5, 0x9b8273d6, 0x88d28022, 0x7ab90321, 0xae7367ca, 0x5c18e4c9, 0x4f48173d, 0xbd23943e, 0xf36e6f75, 0x0105ec76, 0x12551f82, 0xe03e9c81, 0x34f4f86a, 0xc69f7b69, 0xd5cf889d, 0x27a40b9e, 0x79b737ba, 0x8bdcb4b9, 0x988c474d, 0x6ae7c44e, 0xbe2da0a5, 0x4c4623a6, 0x5f16d052, 0xad7d5351 },
    { 0x00000000, 0x13a29877, 0x274530ee, 0x34e7a899, 0x4e8a61dc, 0x5d28f9ab, 0x69cf5132, 0x7a6dc945, 0x9d14c3b8, 0x8eb65bcf, 0xba51f356, 0xa9f36b21, 0xd39ea264, 0xc03c3a13, 0xf4db928a, 0xe7790afd, 0x3fc5f181, 0x2c6769f6, 0x1880c16f, 0x0b225918, 0x714f905d, 0x62ed082a, 0x560aa0b3, 0x45a838c4, 0xa2d13239, 0xb173aa4e, 0x859402d7, 0x96369aa0, 0xec5b53e5, 0xfff9cb92, 0xcb1e630b, 0xd8bcfb7c, 0x7f8be302, 0x6c297b75, 0x58ced3ec, 0x4b6c4b9b, 0x310182de, 0x22a31aa9, 0x1644b230, 0x05e62a47, 0xe29f20ba, 0xf13db8cd, 0xc5da1054, 0xd6788823, 0xac154166, 0xbfb7d911, 0x8b507188, 0x98f2e9ff, 0x404e1283, 0x53ec8af4, 0x670b226d, 0x74a9ba1a, 0x0ec4735f, 0x1d66eb28, 0x298143b1, 0x3a23dbc6, 0xdd5ad13b, 0xcef8494c, 0xfa1fe1d5, 0xe9bd79a2, 0x93d0b0e7, 0x80722890, 0xb4958009, 0xa737187e, 0xff17c604, 0xecb55e73, 0xd852f6ea, 0xcbf06e9d, 0xb19da7d8, 0xa23f3faf, 0x96d89736, 0x857a0f41, 0x620305bc, 0x71a19dcb, 0x45463552, 0x56e4ad25, 0x2c896460, 0x3f2bfc17, 0x0bcc548e, 0x186eccf9, 0xc0d23785, 0xd370aff2, 0xe797076b, 0xf4359f1c, 0x8e585659, 0x9dface2e, 0xa91d66b7, 0xbabffec0, 0x5dc6f43d, 0x4e646c4a, 0x7a83c4d3, 0x69215ca4, 0x134c95e1, 0x00ee0d96, 0x3409a50f, 0x27ab3d78, 0x809c2506, 0x933ebd71, 0xa7d915e8, 0xb47b8d9f, 0xce1644da, 0xddb4dcad, 0xe9537434, 0xfaf1ec43, 0x1d88e6be, 0x0e2a7ec9, 0x3acdd650, 0x296f4e27, 0x53028762, 0x40a01f15, 0x7447b78c, 0x67e52ffb, 0xbf59d487, 0xacfb4cf0, 0x981ce469, 0x8bbe7c1e, 0xf1d3b55b, 0xe2712d2c, 0xd69685b5, 0xc5341dc2, 0x224d173f, 0x31ef8f48, 0x050827d1, 0x16aabfa6, 0x6cc776e3, 0x7f65ee94, 0x4b82460d, 0x5820de7a, 0xfbc3faf9, 0xe861628e, 0xdc86ca17, 0xcf245260, 0xb5499b25, 0xa6eb0352, 0x920cabcb, 0x81ae33bc, 0x66d73941, 0x7575a136, 0x419209af, 0x523091d8, 0x285d589d, 0x3bffc0ea, 0x0f186873, 0x1cbaf004, 0xc4060b78, 0xd7a4930f, 0xe3433b96, 0xf0e1a3e1, 0x8a8c6aa4, 0x992ef2d3, 0xadc95a4a, 0xbe6bc23d, 0x5912c8c0, 0x4ab050b7, 0x7e57f82e, 0x6df56059, 0x1798a91c, 0x043a316b, 0x30dd99f2, 0x237f0185, 0x844819fb, 0x97ea818c, 0xa30d2915, 0xb0afb162, 0xcac27827, 0xd960e050, 0xed8748c9, 0xfe25d0be, 0x195cda43, 0x0afe4234, 0x3e19eaad, 0x2dbb72da, 0x57d6bb9f, 0x447423e8, 0x70938b71, 0x63311306, 0xbb8de87a, 0xa82f700d, 0x9cc8d894, 0x8f6a40e3, 0xf50789a6, 0xe6a511d1, 0xd242b948, 0xc1e0213f, 0x26992bc2, 0x353bb3b5, 0x01dc1b2c, 0x127e835b, 0x68134a1e, 0x7bb1d269, 0x4f567af0, 0x5cf4e287, 0x04d43cfd, 0x1776a48a, 0x23910c13, 0x30339464, 0x4a5e5d21, 0x59fcc556, 0x6d1b6dcf, 0x7eb9f5b8, 0x99c0ff45, 0x8a626732, 0xbe85cfab, 0xad2757dc, 0xd74a9e99, 0xc4e806ee, 0xf00fae77, 0xe3ad3600, 0x3b11cd7c, 0x28b3550b, 0x1c54fd92, 0x0ff665e5, 0x759baca0, 0x663934d7, 0x52de9c4e, 0x417c0439, 0xa6050ec4, 0xb5a796b3, 0x81403e2a, 0x92e2a65d, 0xe88f6f18, 0xfb2df76f, 0xcfca5ff6, 0xdc68c781, 0x7b5fdfff, 0x68fd4788, 0x5c1aef11, 0x4fb87766, 0x35d5be23, 0x26772654, 0x12908ecd, 0x013216ba, 0xe64b1c47, 0xf5e98430, 0xc10e2ca9, 0xd2acb4de, 0xa8c17d9b, 0xbb63e5ec, 0x8f844d75, 0x9c26d502, 0x449a2e7e, 0x5738b609, 0x63df1e90, 0x707d86e7, 0x0a104fa2, 0x19b2d7d5, 0x2d557f4c, 0x3ef7e73b, 0xd98eedc6, 0xca2c75b1, 0xfecbdd28, 0xed69455f, 0x97048c1a, 0x84a6146d, 0xb041bcf4, 0xa3e32483 },
    { 0x00000000, 0xa541927e, 0x4f6f520d, 0xea2ec073, 0x9edea41a, 0x3b9f3664, 0xd1b1f617, 0x74f06469, 0x38513ec5, 0x9d10acbb, 0x773e6cc8, 0xd27ffeb6, 0xa68f9adf, 0x03ce08a1, 0xe9e0c8d2, 0x4ca15aac, 0x70a27d8a, 0xd5e3eff4, 0x3fcd2f87, 0x9a8cbdf9, 0xee7cd990, 0x4b3d4bee, 0xa1138b9d, 0x045219e3, 0x48f3434f, 0xedb2d131, 0x079c1142, 0xa2dd833c, 0xd62de755, 0x736c752b, 0x9942b558, 0x3c032726, 0xe144fb14, 0x4405696a, 0xae2ba919, 0x0b6a3b67, 0x7f9a5f0e, 0xdadbcd70, 0x30f50d03, 0x95b49f7d, 0xd915c5d1, 0x7c5457af, 0x967a97dc, 0x333b05a2, 0x47cb61cb, 0xe28af3b5, 0x08a433c6, 0xade5a1b8, 0x91e6869e, 0x34a714e0, 0xde89d493, 0x7bc846ed, 0x0f382284, 0xaa79b0fa, 0x40577089, 0xe516e2f7, 0xa9b7b85b, 0x0cf62a25, 0xe6d8ea56, 0x43997828, 0x37691c41, 0x92288e3f, 0x78064e4c, 0xdd47dc32, 0xc76580d9, 0x622412a7, 0x880ad2d4, 0x2d4b40aa, 0x59bb24c3, 0xfcfab6bd, 0x16d476ce, 0xb395e4b0, 0xff34be1c, 0x5a752c62, 0xb05bec11, 0x151a7e6f, 0x61ea1a06, 0xc4ab8878, 0x2e85480b, 0x8bc4da75, 0xb7c7fd53, 0x12866f2d, 0xf8a8af5e, 0x5de93d20, 0x29195949, 0x8c58cb37, 0x66760b44, 0xc337993a, 0x8f96c396, 0x2ad751e8, 0xc0f9919b, 0x65b803e5, 0x1148678c, 0xb409f5f2, 0x5e273581, 0xfb66a7ff, 0x26217bcd, 0x8360e9b3, 0x694e29c0, 0xcc0fbbbe, 0xb8ffdfd7, 0x1dbe4da9, 0xf7908dda, 0x52d11fa4, 0x1e704508, 0xbb31d776, 0x511f1705, 0xf45e857b, 0x80aee112, 0x25ef736c, 0xcfc1b31f, 0x6a802161, 0x56830647, 0xf3c29439, 0x19ec544a, 0xbcadc634, 0xc85da25d, 0x6d1c3023, 0x8732f050, 0x2273622e, 0x6ed23882, 0xcb93aafc, 0x21bd6a8f, 0x84fcf8f1, 0xf00c9c98, 0x554d0ee6, 0xbf63ce95, 0x1a225ceb, 0x8b277743, 0x2e66e53d, 0xc448254e, 0x6109b730, 0x15f9d359, 0xb0b84127, 0x5a968154, 0xffd7132a, 0xb3764986, 0x1637dbf8, 0xfc191b8b, 0x595889f5, 0x2da8ed9c, 0x88e97fe2, 0x62c7bf91, 0xc7862def, 0xfb850ac9, 0x5ec498b7, 0xb4ea58c4, 0x11abcaba, 0x655baed3, 0xc01a3cad, 0x2a34fcde, 0x8f756ea0, 0xc3d4340c, 0x6695a672, 0x8cbb6601, 0x29faf47f, 0x5d0a9016, 0xf84b0268, 0x1265c21b, 0xb7245065, 0x6a638c57, 0xcf221e29, 0x250cde5a, 0x804d4c24, 0xf4bd284d, 0x51fcba33, 0xbbd27a40, 0x1e93e83e, 0x5232b292, 0xf77320ec, 0x1d5de09f, 0xb81c72e1, 0xccec1688, 0x69ad84f6, 0x83834485, 0x26c2d6fb, 0x1ac1f1dd, 0xbf8063a3, 0x55aea3d0, 0xf0ef31ae, 0x841f55c7, 0x215ec7b9, 0xcb7007ca, 0x6e3195b4, 0x2290cf18, 0x87d15d66, 0x6dff9d15, 0xc8be0f6b, 0xbc4e6b02, 0x190ff97c, 0xf321390f, 0x5660ab71, 0x4c42f79a, 0xe90365e4, 0x032da597, 0xa66c37e9, 0xd29c5380, 0x77ddc1fe, 0x9df3018d, 0x38b293f3, 0x7413c95f, 0xd1525b21, 0x3b7c9b52, 0x9e3d092c, 0xeacd6d45, 0x4f8cff3b, 0xa5a23f48, 0x00e3ad36, 0x3ce08a10, 0x99a1186e, 0x738fd81d, 0xd6ce4a63, 0xa23e2e0a, 0x077fbc74, 0xed517c07, 0x4810ee79, 0x04b1b4d5, 0xa1f026ab, 0x4bdee6d8, 0xee9f74a6, 0x9a6f10cf, 0x3f2e82b1, 0xd50042c2, 0x7041d0bc, 0xad060c8e, 0x08479ef0, 0xe2695e83, 0x4728ccfd, 0x33d8a894, 0x96993aea, 0x7cb7fa99, 0xd9f668e7, 0x9557324b, 0x3016a035, 0xda386046, 0x7f79f238, 0x0b899651, 0xaec8042f, 0x44e6c45c, 0xe1a75622, 0xdda47104, 0x78e5e37a, 0x92cb2309, 0x378ab177, 0x437ad51e, 0xe63b4760, 0x0c158713, 0xa954156d, 0xe5f54fc1, 0x40b4ddbf, 0xaa9a1dcc, 0x0fdb8fb2, 0x7b2bebdb, 0xde6a79a5, 0x3444b9d6, 0x91052ba8 },
    { 0x00000000, 0xdd45aab8, 0xbf672381, 0x62228939, 0x7b2231f3, 0xa6679b4b, 0xc4451272, 0x1900b8ca, 0xf64463e6, 0x2b01c95e, 0x49234067, 0x9466eadf, 0x8d665215, 0x5023f8ad, 0x32017194, 0xef44db2c, 0xe964b13d, 0x34211b85, 0x560392bc, 0x8b463804, 0x924680ce, 0x4f032a76, 0x2d21a34f, 0xf06409f7, 0x1f20d2db, 0xc2657863, 0xa047f15a, 0x7d025be2, 0x6402e328, 0xb9474990, 0xdb65c0a9, 0x06206a11, 0xd725148b, 0x0a60be33, 0x6842370a, 0xb5079db2, 0xac072578, 0x71428fc0, 0x136006f9, 0xce25ac41, 0x2161776d, 0xfc24ddd5, 0x9e0654ec, 0x4343fe54, 0x5a43469e, 0x8706ec26, 0xe524651f, 0x3861cfa7, 0x3e41a5b6, 0xe3040f0e, 0x81268637, 0x5c632c8f, 0x45639445, 0x98263efd, 0xfa04b7c4, 0x27411d7c, 0xc805c650, 0x15406ce8, 0x7762e5d1, 0xaa274f69, 0xb327f7a3, 0x6e625d1b, 0x0c40d422, 0xd1057e9a, 0xaba65fe7, 0x76e3f55f, 0x14c17c66, 0xc984d6de, 0xd0846e14, 0x0dc1c4ac, 0x6fe34d95, 0xb2a6e72d, 0x5de23c01, 0x80a796b9, 0xe2851f80, 0x3fc0b538, 0x26c00df2, 0xfb85a74a, 0x99a72e73, 0x44e284cb, 0x42c2eeda, 0x9f874462, 0xfda5cd5b, 0x20e067e3, 0x39e0df29, 0xe4a57591, 0x8687fca8, 0x5bc25610, 0xb4868d3c, 0x69c32784, 0x0be1aebd, 0xd6a40405, 0xcfa4bccf, 0x12e11677, 0x70c39f4e, 0xad8635f6, 0x7c834b6c, 0xa1c6e1d4, 0xc3e468ed, 0x1ea1c255, 0x07a17a9f, 0xdae4d027, 0xb8c6591e, 0x6583f3a6, 0x8ac7288a, 0x57828232, 0x35a00b0b, 0xe8e5a1b3, 0xf1e51979, 0x2ca0b3c1, 0x4e823af8, 0x93c79040, 0x95e7fa51, 0x48a250e9, 0x2a80d9d0, 0xf7c57368, 0xeec5cba2, 0x3380611a, 0x51a2e823, 0x8ce7429b, 0x63a399b7, 0xbee6330f, 0xdcc4ba36, 0x0181108e, 0x1881a844, 0xc5c402fc, 0xa7e68bc5, 0x7aa3217d, 0x52a0c93f, 0x8fe56387, 0xedc7eabe, 0x30824006, 0x2982f8cc, 0xf4c75274, 0x96e5db4d, 0x4ba071f5, 0xa4e4aad9, 0x79a10061, 0x1b838958, 0xc6c623e0, 0xdfc69b2a, 0x02833192, 0x60a1b8ab, 0xbde41213, 0xbbc47802, 0x6681d2ba, 0x04a35b83, 0xd9e6f13b, 0xc0e649f1, 0x1da3e349, 0x7f816a70, 0xa2c4c0c8, 0x4d801be4, 0x90c5b15c, 0xf2e73865, 0x2fa292dd, 0x36a22a17, 0xebe780af, 0x89c50996, 0x5480a32e, 0x8585ddb4, 0x58c0770c, 0x3ae2fe35, 0xe7a7548d, 0xfea7ec47, 0x23e246ff, 0x41c0cfc6, 0x9c85657e, 0x73c1be52, 0xae8414ea, 0xcca69dd3, 0x11e3376b, 0x08e38fa1, 0xd5a62519, 0xb784ac20, 0x6ac10698, 0x6ce16c89, 0xb1a4c631, 0xd3864f08, 0x0ec3e5b0, 0x17c35d7a, 0xca86f7c2, 0xa8a47efb, 0x75e1d443, 0x9aa50f6f, 0x47e0a5d7, 0x25c22cee, 0xf8878656, 0xe1873e9c, 0x3cc29424, 0x5ee01d1d, 0x83a5b7a5, 0xf90696d8, 0x24433c60, 0x4661b559, 0x9b241fe1, 0x8224a72b, 0x5f610d93, 0x3d4384aa, 0xe0062e12, 0x0f42f53e, 0xd2075f86, 0xb025d6bf, 0x6d607c07, 0x7460c4cd, 0xa9256e75, 0xcb07e74c, 0x16424df4, 0x106227e5, 0xcd278d5d, 0xaf050464, 0x7240aedc, 0x6b401616, 0xb605bcae, 0xd4273597, 0x09629f2f, 0xe6264403, 0x3b63eebb, 0x59416782, 0x8404cd3a, 0x9d0475f0, 0x4041df48, 0x22635671, 0xff26fcc9, 0x2e238253, 0xf36628eb, 0x9144a1d2, 0x4c010b6a, 0x5501b3a0, 0x88441918, 0xea669021, 0x37233a99, 0xd867e1b5, 0x05224b0d, 0x6700c234, 0xba45688c, 0xa345d046, 0x7e007afe, 0x1c22f3c7, 0xc167597f, 0xc747336e, 0x1a0299d6, 0x782010ef, 0xa565ba57, 0xbc65029d, 0x6120a825, 0x0302211c, 0xde478ba4, 0x31035088, 0xec46fa30, 0x8e647309, 0x5321d9b1, 0x4a21617b, 0x9764cbc3, 0xf54642fa, 0x2803e842 },
    { 0x00000000, 0x38116fac, 0x7022df58, 0x4833b0f4, 0xe045beb0, 0xd854d11c, 0x906761e8, 0xa8760e44, 0xc5670b91, 0xfd76643d, 0xb545d4c9, 0x8d54bb65, 0x2522b521, 0x1d33da8d, 0x55006a79, 0x6d1105d5, 0x8f2261d3, 0xb7330e7f, 0xff00be8b, 0xc711d127, 0x6f67df63, 0x5776b0cf, 0x1f45003b, 0x27546f97, 0x4a456a42, 0x725405ee, 0x3a67b51a, 0x0276dab6, 0xaa00d4f2, 0x9211bb5e, 0xda220baa, 0xe2336406, 0x1ba8b557, 0x23b9dafb, 0x6b8a6a0f, 0x539b05a3, 0xfbed0be7, 0xc3fc644b, 0x8bcfd4bf, 0xb3debb13, 0xdecfbec6, 0xe6ded16a, 0xaeed619e, 0x96fc0e32, 0x3e8a0076, 0x069b6fda, 0x4ea8df2e, 0x76b9b082, 0x948ad484, 0xac9bbb28, 0xe4a80bdc, 0xdcb96470, 0x74cf6a34, 0x4cde0598, 0x04edb56c, 0x3cfcdac0, 0x51eddf15, 0x69fcb0b9, 0x21cf004d, 0x19de6fe1, 0xb1a861a5, 0x89b90e09, 0xc18abefd, 0xf99bd151, 0x37516aae, 0x0f400502, 0x4773b5f6, 0x7f62da5a, 0xd714d41e, 0xef05bbb2, 0xa7360b46, 0x9f2764ea, 0xf236613f, 0xca270e93, 0x8214be67, 0xba05d1cb, 0x1273df8f, 0x2a62b023, 0x625100d7, 0x5a406f7b, 0xb8730b7d, 0x806264d1, 0xc851d425, 0xf040bb89, 0x5836b5cd, 0x6027da61, 0x28146a95, 0x10050539, 0x7d1400ec, 0x45056f40, 0x0d36dfb4, 0x3527b018, 0x9d51be5c, 0xa540d1f0, 0xed736104, 0xd5620ea8, 0x2cf9dff9, 0x14e8b055, 0x5cdb00a1, 0x64ca6f0d, 0xccbc6149, 0xf4ad0ee5, 0xbc9ebe11, 0x848fd1bd, 0xe99ed468, 0xd18fbbc4, 0x99bc0b30, 0xa1ad649c, 0x09db6ad8, 0x31ca0574, 0x79f9b580, 0x41e8da2c, 0xa3dbbe2a, 0x9bcad186, 0xd3f96172, 0xebe80ede, 0x439e009a, 0x7b8f6f36, 0x33bcdfc2, 0x0badb06e, 0x66bcb5bb, 0x5eadda17, 0x169e6ae3, 0x2e8f054f, 0x86f90b0b, 0xbee864a7, 0xf6dbd453, 0xcecabbff, 0x6ea2d55c, 0x56b3baf0, 0x1e800a04, 0x269165a8, 0x8ee76bec, 0xb6f60440, 0xfec5b4b4, 0xc6d4db18, 0xabc5decd, 0x93d4b161, 0xdbe70195, 0xe3f66e39, 0x4b80607d, 0x73910fd1, 0x3ba2bf25, 0x03b3d089, 0xe180b48f, 0xd991db23, 0x91a26bd7, 0xa9b3047b, 0x01c50a3f, 0x39d46593, 0x71e7d567, 0x49f6bacb, 0x24e7bf1e, 0x1cf6d0b2, 0x54c56046, 0x6cd40fea, 0xc4a201ae, 0xfcb36e02, 0xb480def6, 0x8c91b15a, 0x750a600b, 0x4d1b0fa7, 0x0528bf53, 0x3d39d0ff, 0x954fdebb, 0xad5eb117, 0xe56d01e3, 0xdd7c6e4f, 0xb06d6b9a, 0x887c0436, 0xc04fb4c2, 0xf85edb6e, 0x5028d52a, 0x6839ba86, 0x200a0a72, 0x181b65de, 0xfa2801d8, 0xc2396e74, 0x8a0ade80, 0xb21bb12c, 0x1a6dbf68, 0x227cd0c4, 0x6a4f6030, 0x525e0f9c, 0x3f4f0a49, 0x075e65e5, 0x4f6dd511, 0x777cbabd, 0xdf0ab4f9, 0xe71bdb55, 0xaf286ba1, 0x9739040d, 0x59f3bff2, 0x61e2d05e, 0x29d160aa, 0x11c00f06, 0xb9b60142, 0x81a76eee, 0xc994de1a, 0xf185b1b6, 0x9c94b463, 0xa485dbcf, 0xecb66b3b, 0xd4a70497, 0x7cd10ad3, 0x44c0657f, 0x0cf3d58b, 0x34e2ba27, 0xd6d1de21, 0xeec0b18d, 0xa6f30179, 0x9ee26ed5, 0x36946091, 0x0e850f3d, 0x46b6bfc9, 0x7ea7d065, 0x13b6d5b0, 0x2ba7ba1c, 0x63940ae8, 0x5b856544, 0xf3f36b00, 0xcbe204ac, 0x83d1b458, 0xbbc0dbf4, 0x425b0aa5, 0x7a4a6509, 0x3279d5fd, 0x0a68ba51, 0xa21eb415, 0x9a0fdbb9, 0xd23c6b4d, 0xea2d04e1, 0x873c0134, 0xbf2d6e98, 0xf71ede6c, 0xcf0fb1c0, 0x6779bf84, 0x5f68d028, 0x175b60dc, 0x2f4a0f70, 0xcd796b76, 0xf56804da, 0xbd5bb42e, 0x854adb82, 0x2d3cd5c6, 0x152dba6a, 0x5d1e0a9e, 0x650f6532, 0x081e60e7, 0x300f0f4b, 0x783cbfbf, 0x402dd013, 0xe85bde57, 0xd04ab1fb, 0x9879010f, 0xa0686ea3 },
    { 0x00000000, 0xef306b19, 0xdb8ca0c3, 0x34bccbda, 0xb2f53777, 0x5dc55c6e, 0x697997b4, 0x8649fcad, 0x6006181f, 0x8f367306, 0xbb8ab8dc, 0x54bad3c5, 0xd2f32f68, 0x3dc34471, 0x097f8fab, 0xe64fe4b2, 0xc00c303e, 0x2f3c5b27, 0x1b8090fd, 0xf4b0fbe4, 0x72f90749, 0x9dc96c50, 0xa975a78a, 0x4645cc93, 0xa00a2821, 0x4f3a4338, 0x7b8688e2, 0x94b6e3fb, 0x12ff1f56, 0xfdcf744f, 0xc973bf95, 0x2643d48c, 0x85f4168d, 0x6ac47d94, 0x5e78b64e, 0xb148dd57, 0x370121fa, 0xd8314ae3, 0xec8d8139, 0x03bdea20, 0xe5f20e92, 0x0ac2658b, 0x3e7eae51, 0xd14ec548, 0x570739e5, 0xb83752fc, 0x8c8b9926, 0x63bbf23f, 0x45f826b3, 0xaac84daa, 0x9e748670, 0x7144ed69, 0xf70d11c4, 0x183d7add, 0x2c81b107, 0xc3b1da1e, 0x25fe3eac, 0xcace55b5, 0xfe729e6f, 0x1142f576, 0x970b09db, 0x783b62c2, 0x4c87a918, 0xa3b7c201, 0x0e045beb, 0xe13430f2, 0xd588fb28, 0x3ab89031, 0xbcf16c9c, 0x53c10785, 0x677dcc5f, 0x884da746, 0x6e0243f4, 0x813228ed, 0xb58ee337, 0x5abe882e, 0xdcf77483, 0x33c71f9a, 0x077bd440, 0xe84bbf59, 0xce086bd5, 0x213800cc, 0x1584cb16, 0xfab4a00f, 0x7cfd5ca2, 0x93cd37bb, 0xa771fc61, 0x48419778, 0xae0e73ca, 0x413e18d3, 0x7582d309, 0x9ab2b810, 0x1cfb44bd, 0xf3cb2fa4, 0xc777e47e, 0x28478f67, 0x8bf04d66, 0x64c0267f, 0x507ceda5, 0xbf4c86bc, 0x39057a11, 0xd6351108, 0xe289dad2, 0x0db9b1cb, 0xebf65579, 0x04c63e60, 0x307af5ba, 0xdf4a9ea3, 0x5903620e, 0xb6330917, 0x828fc2cd, 0x6dbfa9d4, 0x4bfc7d58, 0xa4cc1641, 0x9070dd9b, 0x7f40b682, 0xf9094a2f, 0x16392136, 0x2285eaec, 0xcdb581f5, 0x2bfa6547, 0xc4ca0e5e, 0xf076c584, 0x1f46ae9d, 0x990f5230, 0x763f3929, 0x4283f2f3, 0xadb399ea, 0x1c08b7d6, 0xf338dccf, 0xc7841715, 0x28b47c0c, 0xaefd80a1, 0x41cdebb8, 0x75712062, 0x9a414b7b, 0x7c0eafc9, 0x933ec4d0, 0xa7820f0a, 0x48b26413, 0xcefb98be, 0x21cbf3a7, 0x1577387d, 0xfa475364, 0xdc0487e8, 0x3334ecf1, 0x0788272b, 0xe8b84c32, 0x6ef1b09f, 0x81c1db86, 0xb57d105c, 0x5a4d7b45, 0xbc029ff7, 0x5332f4ee, 0x678e3f34, 0x88be542d, 0x0ef7a880, 0xe1c7c399, 0xd57b0843, 0x3a4b635a, 0x99fca15b, 0x76ccca42, 0x42700198, 0xad406a81, 0x2b09962c, 0xc439fd35, 0xf08536ef, 0x1fb55df6, 0xf9fab944, 0x16cad25d, 0x22761987, 0xcd46729e, 0x4b0f8e33, 0xa43fe52a, 0x90832ef0, 0x7fb345e9, 0x59f09165, 0xb6c0fa7c, 0x827c31a6, 0x6d4c5abf, 0xeb05a612, 0x0435cd0b, 0x308906d1, 0xdfb96dc8, 0x39f6897a, 0xd6c6e263, 0xe27a29b9, 0x0d4a42a0, 0x8b03be0d, 0x6433d514, 0x508f1ece, 0xbfbf75d7, 0x120cec3d, 0xfd3c8724, 0xc9804cfe, 0x26b027e7, 0xa0f9db4a, 0x4fc9b053, 0x7b757b89, 0x94451090, 0x720af422, 0x9d3a9f3b, 0xa98654e1, 0x46b63ff8, 0xc0ffc355, 0x2fcfa84c, 0x1b736396, 0xf443088f, 0xd200dc03, 0x3d30b71a, 0x098c7cc0, 0xe6bc17d9, 0x60f5eb74, 0x8fc5806d, 0xbb794bb7, 0x544920ae, 0xb206c41c, 0x5d36af05, 0x698a64df, 0x86ba0fc6, 0x00f3f36b, 0xefc39872, 0xdb7f53a8, 0x344f38b1, 0x97f8fab0, 0x78c891a9, 0x4c745a73, 0xa344316a, 0x250dcdc7, 0xca3da6de, 0xfe816d04, 0x11b1061d, 0xf7fee2af, 0x18ce89b6, 0x2c72426c, 0xc3422975, 0x450bd5d8, 0xaa3bbec1, 0x9e87751b, 0x71b71e02, 0x57f4ca8e, 0xb8c4a197, 0x8c786a4d, 0x63480154, 0xe501fdf9, 0x0a3196e0, 0x3e8d5d3a, 0xd1bd3623, 0x37f2d291, 0xd8c2b988, 0xec7e7252, 0x034e194b, 0x8507e5e6, 0x6a378eff, 0x5e8b4525, 0xb1bb2e3c },
    { 0x00000000, 0x68032cc8, 0xd0065990, 0xb8057558, 0xa5e0c5d1, 0xcde3e919, 0x75e69c41, 0x1de5b089, 0x4e2dfd53, 0x262ed19b, 0x9e2ba4c3, 0xf628880b, 0xebcd3882, 0x83ce144a, 0x3bcb6112, 0x53c84dda, 0x9c5bfaa6, 0xf458d66e, 0x4c5da336, 0x245e8ffe, 0x39bb3f77, 0x51b813bf, 0xe9bd66e7, 0x81be4a2f, 0xd27607f5, 0xba752b3d, 0x02705e65, 0x6a7372ad, 0x7796c224, 0x1f95eeec, 0xa7909bb4, 0xcf93b77c, 0x3d5b83bd, 0x5558af75, 0xed5dda2d, 0x855ef6e5, 0x98bb466c, 0xf0b86aa4, 0x48bd1ffc, 0x20be3334, 0x73767eee, 0x1b755226, 0xa370277e, 0xcb730bb6, 0xd696bb3f, 0xbe9597f7, 0x0690e2af, 0x6e93ce67, 0xa100791b, 0xc90355d3, 0x7106208b, 0x19050c43, 0x04e0bcca, 0x6ce39002, 0xd4e6e55a, 0xbce5c992, 0xef2d8448, 0x872ea880, 0x3f2bddd8, 0x5728f110, 0x4acd4199, 0x22ce6d51, 0x9acb1809, 0xf2c834c1, 0x7ab7077a, 0x12b42bb2, 0xaab15eea, 0xc2b27222, 0xdf57c2ab, 0xb754ee63, 0x0f519b3b, 0x6752b7f3, 0x349afa29, 0x5c99d6e1, 0xe49ca3b9, 0x8c9f8f71, 0x917a3ff8, 0xf9791330, 0x417c6668, 0x297f4aa0, 0xe6ecfddc, 0x8eefd114, 0x36eaa44c, 0x5ee98884, 0x430c380d, 0x2b0f14c5, 0x930a619d, 0xfb094d55, 0xa8c1008f, 0xc0c22c47, 0x78c7591f, 0x10c475d7, 0x0d21c55e, 0x6522e996, 0xdd279cce, 0xb524b006, 0x47ec84c7, 0x2fefa80f, 0x97eadd57, 0xffe9f19f, 0xe20c4116, 0x8a0f6dde, 0x320a1886, 0x5a09344e, 0x09c17994, 0x61c2555c, 0xd9c72004, 0xb1c40ccc, 0xac21bc45, 0xc422908d, 0x7c27e5d5, 0x1424c91d, 0xdbb77e61, 0xb3b452a9, 0x0bb127f1, 0x63b20b39, 0x7e57bbb0, 0x16549778, 0xae51e220, 0xc652cee8, 0x959a8332, 0xfd99affa, 0x459cdaa2, 0x2d9ff66a, 0x307a46e3, 0x58796a2b, 0xe07c1f73, 0x887f33bb, 0xf56e0ef4, 0x9d6d223c, 0x25685764, 0x4d6b7bac, 0x508ecb25, 0x388de7ed, 0x808892b5, 0xe88bbe7d, 0xbb43f3a7, 0xd340df6f, 0x6b45aa37, 0x034686ff, 0x1ea33676, 0x76a01abe, 0xcea56fe6, 0xa6a6432e, 0x6935f452, 0x0136d89a, 0xb933adc2, 0xd130810a, 0xccd53183, 0xa4d61d4b, 0x1cd36813, 0x74d044db, 0x27180901, 0x4f1b25c9, 0xf71e5091, 0x9f1d7c59, 0x82f8ccd0, 0xeafbe018, 0x52fe9540, 0x3afdb988, 0xc8358d49, 0xa036a181, 0x1833d4d9, 0x7030f811, 0x6dd54898, 0x05d66450, 0xbdd31108, 0xd5d03dc0, 0x8618701a, 0xee1b5cd2, 0x561e298a, 0x3e1d0542, 0x23f8b5cb, 0x4bfb9903, 0xf3feec5b, 0x9bfdc093, 0x546e77ef, 0x3c6d5b27, 0x84682e7f, 0xec6b02b7, 0xf18eb23e, 0x998d9ef6, 0x2188ebae, 0x498bc766, 0x1a438abc, 0x7240a674, 0xca45d32c, 0xa246ffe4, 0xbfa34f6d, 0xd7a063a5, 0x6fa516fd, 0x07a63a35, 0x8fd9098e, 0xe7da2546, 0x5fdf501e, 0x37dc7cd6, 0x2a39cc5f, 0x423ae097, 0xfa3f95cf, 0x923cb907, 0xc1f4f4dd, 0xa9f7d815, 0x11f2ad4d, 0x79f18185, 0x6414310c, 0x0c171dc4, 0xb412689c, 0xdc114454, 0x1382f328, 0x7b81dfe0, 0xc384aab8, 0xab878670, 0xb66236f9, 0xde611a31, 0x66646f69, 0x0e6743a1, 0x5daf0e7b, 0x35ac22b3, 0x8da957eb, 0xe5aa7b23, 0xf84fcbaa, 0x904ce762, 0x2849923a, 0x404abef2, 0xb2828a33, 0xda81a6fb, 0x6284d3a3, 0x0a87ff6b, 0x17624fe2, 0x7f61632a, 0xc7641672, 0xaf673aba, 0xfcaf7760, 0x94ac5ba8, 0x2ca92ef0, 0x44aa0238, 0x594fb2b1, 0x314c9e79, 0x8949eb21, 0xe14ac7e9, 0x2ed97095, 0x46da5c5d, 0xfedf2905, 0x96dc05cd, 0x8b39b544, 0xe33a998c, 0x5b3fecd4, 0x333cc01c, 0x60f48dc6, 0x08f7a10e, 0xb0f2d456, 0xd8f1f89e, 0xc5144817, 0xad1764df, 0x15121187, 0x7d113d4f },
    { 0x00000000, 0x493c7d27, 0x9278fa4e, 0xdb448769, 0x211d826d, 0x6821ff4a, 0xb3657823, 0xfa590504, 0x423b04da, 0x0b0779fd, 0xd043fe94, 0x997f83b3, 0x632686b7, 0x2a1afb90, 0xf15e7cf9, 0xb86201de, 0x847609b4, 0xcd4a7493, 0x160ef3fa, 0x5f328edd, 0xa56b8bd9, 0xec57f6fe, 0x37137197, 0x7e2f0cb0, 0xc64d0d6e, 0x8f717049, 0x5435f720, 0x1d098a07, 0xe7508f03, 0xae6cf224, 0x7528754d, 0x3c14086a, 0x0d006599, 0x443c18be, 0x9f789fd7, 0xd644e2f0, 0x2c1de7f4, 0x65219ad3, 0xbe651dba, 0xf759609d, 0x4f3b6143, 0x06071c64, 0xdd439b0d, 0x947fe62a, 0x6e26e32e, 0x271a9e09, 0xfc5e1960, 0xb5626447, 0x89766c2d, 0xc04a110a, 0x1b0e9663, 0x5232eb44, 0xa86bee40, 0xe1579367, 0x3a13140e, 0x732f6929, 0xcb4d68f7, 0x827115d0, 0x593592b9, 0x1009ef9e, 0xea50ea9a, 0xa36c97bd, 0x782810d4, 0x31146df3, 0x1a00cb32, 0x533cb615, 0x8878317c, 0xc1444c5b, 0x3b1d495f, 0x72213478, 0xa965b311, 0xe059ce36, 0x583bcfe8, 0x1107b2cf, 0xca4335a6, 0x837f4881, 0x79264d85, 0x301a30a2, 0xeb5eb7cb, 0xa262caec, 0x9e76c286, 0xd74abfa1, 0x0c0e38c8, 0x453245ef, 0xbf6b40eb, 0xf6573dcc, 0x2d13baa5, 0x642fc782, 0xdc4dc65c, 0x9571bb7b, 0x4e353c12, 0x07094135, 0xfd504431, 0xb46c3916, 0x6f28be7f, 0x2614c358, 0x1700aeab, 0x5e3cd38c, 0x857854e5, 0xcc4429c2, 0x361d2cc6, 0x7f2151e1, 0xa465d688, 0xed59abaf, 0x553baa71, 0x1c07d756, 0xc743503f, 0x8e7f2d18, 0x7426281c, 0x3d1a553b, 0xe65ed252, 0xaf62af75, 0x9376a71f, 0xda4ada38, 0x010e5d51, 0x48322076, 0xb26b2572, 0xfb575855, 0x2013df3c, 0x692fa21b, 0xd14da3c5, 0x9871dee2, 0x4335598b, 0x0a0924ac, 0xf05021a8, 0xb96c5c8f, 0x6228dbe6, 0x2b14a6c1, 0x34019664, 0x7d3deb43, 0xa6796c2a, 0xef45110d, 0x151c1409, 0x5c20692e, 0x8764ee47, 0xce589360, 0x763a92be, 0x3f06ef99, 0xe44268f0, 0xad7e15d7, 0x572710d3, 0x1e1b6df4, 0xc55fea9d, 0x8c6397ba, 0xb0779fd0, 0xf94be2f7, 0x220f659e, 0x6b3318b9, 0x916a1dbd, 0xd856609a, 0x0312e7f3, 0x4a2e9ad4, 0xf24c9b0a, 0xbb70e62d, 0x60346144, 0x29081c63, 0xd3511967, 0x9a6d6440, 0x4129e329, 0x08159e0e, 0x3901f3fd, 0x703d8eda, 0xab7909b3, 0xe2457494, 0x181c7190, 0x51200cb7, 0x8a648bde, 0xc358f6f9, 0x7b3af727, 0x32068a00, 0xe9420d69, 0xa07e704e, 0x5a27754a, 0x131b086d, 0xc85f8f04, 0x8163f223, 0xbd77fa49, 0xf44b876e, 0x2f0f0007, 0x66337d20, 0x9c6a7824, 0xd5560503, 0x0e12826a, 0x472eff4d, 0xff4cfe93, 0xb67083b4, 0x6d3404dd, 0x240879fa, 0xde517cfe, 0x976d01d9, 0x4c2986b0, 0x0515fb97, 0x2e015d56, 0x673d2071, 0xbc79a718, 0xf545da3f, 0x0f1cdf3b, 0x4620a21c, 0x9d642575, 0xd4585852, 0x6c3a598c, 0x250624ab, 0xfe42a3c2, 0xb77edee5, 0x4d27dbe1, 0x041ba6c6, 0xdf5f21af, 0x96635c88, 0xaa7754e2, 0xe34b29c5, 0x380faeac, 0x7133d38b, 0x8b6ad68f, 0xc256aba8, 0x19122cc1, 0x502e51e6, 0xe84c5038, 0xa1702d1f, 0x7a34aa76, 0x3308d751, 0xc951d255, 0x806daf72, 0x5b29281b, 0x1215553c, 0x230138cf, 0x6a3d45e8, 0xb179c281, 0xf845bfa6, 0x021cbaa2, 0x4b20c785, 0x906440ec, 0xd9583dcb, 0x613a3c15, 0x28064132, 0xf342c65b, 0xba7ebb7c, 0x4027be78, 0x091bc35f, 0xd25f4436, 0x9b633911, 0xa777317b, 0xee4b4c5c, 0x350fcb35, 0x7c33b612, 0x866ab316, 0xcf56ce31, 0x14124958, 0x5d2e347f, 0xe54c35a1, 0xac704886, 0x7734cfef, 0x3e08b2c8, 0xc451b7cc, 0x8d6dcaeb, 0x56294d82, 0x1f1530a5 },
    { 0x00000000, 0xf43ed648, 0xed91da61, 0x19af0c29, 0xdecfc233, 0x2af1147b, 0x335e1852, 0xc760ce1a, 0xb873f297, 0x4c4d24df, 0x55e228f6, 0xa1dcfebe, 0x66bc30a4, 0x9282e6ec, 0x8b2deac5, 0x7f133c8d, 0x750b93df, 0x81354597, 0x989a49be, 0x6ca49ff6, 0xabc451ec, 0x5ffa87a4, 0x46558b8d, 0xb26b5dc5, 0xcd786148, 0x3946b700, 0x20e9bb29, 0xd4d76d61, 0x13b7a37b, 0xe7897533, 0xfe26791a, 0x0a18af52, 0xea1727be, 0x1e29f1f6, 0x0786fddf, 0xf3b82b97, 0x34d8e58d, 0xc0e633c5, 0xd9493fec, 0x2d77e9a4, 0x5264d529, 0xa65a0361, 0xbff50f48, 0x4bcbd900, 0x8cab171a, 0x7895c152, 0x613acd7b, 0x95041b33, 0x9f1cb461, 0x6b226229, 0x728d6e00, 0x86b3b848, 0x41d37652, 0xb5eda01a, 0xac42ac33, 0x587c7a7b, 0x276f46f6, 0xd35190be, 0xcafe9c97, 0x3ec04adf, 0xf9a084c5, 0x0d9e528d, 0x14315ea4, 0xe00f88ec, 0xd1c2398d, 0x25fcefc5, 0x3c53e3ec, 0xc86d35a4, 0x0f0dfbbe, 0xfb332df6, 0xe29c21df, 0x16a2f797, 0x69b1cb1a, 0x9d8f1d52, 0x8420117b, 0x701ec733, 0xb77e0929, 0x4340df61, 0x5aefd348, 0xaed10500, 0xa4c9aa52, 0x50f77c1a, 0x49587033, 0xbd66a67b, 0x7a066861, 0x8e38be29, 0x9797b200, 0x63a96448, 0x1cba58c5, 0xe8848e8d, 0xf12b82a4, 0x051554ec, 0xc2759af6, 0x364b4cbe, 0x2fe44097, 0xdbda96df, 0x3bd51e33, 0xcfebc87b, 0xd644c452, 0x227a121a, 0xe51adc00, 0x11240a48, 0x088b0661, 0xfcb5d029, 0x83a6eca4, 0x77983aec, 0x6e3736c5, 0x9a09e08d, 0x5d692e97, 0xa957f8df, 0xb0f8f4f6, 0x44c622be, 0x4ede8dec, 0xbae05ba4, 0xa34f578d, 0x577181c5, 0x90114fdf, 0x642f9997, 0x7d8095be, 0x89be43f6, 0xf6ad7f7b, 0x0293a933, 0x1b3ca51a, 0xef027352, 0x2862bd48, 0xdc5c6b00, 0xc5f36729, 0x31cdb161, 0xa66805eb, 0x5256d3a3, 0x4bf9df8a, 0xbfc709c2, 0x78a7c7d8, 0x8c991190, 0x95361db9, 0x6108cbf1, 0x1e1bf77c, 0xea252134, 0xf38a2d1d, 0x07b4fb55, 0xc0d4354f, 0x34eae307, 0x2d45ef2e, 0xd97b3966, 0xd3639634, 0x275d407c, 0x3ef24c55, 0xcacc9a1d, 0x0dac5407, 0xf992824f, 0xe03d8e66, 0x1403582e, 0x6b1064a3, 0x9f2eb2eb, 0x8681bec2, 0x72bf688a, 0xb5dfa690, 0x41e170d8, 0x584e7cf1, 0xac70aab9, 0x4c7f2255, 0xb841f41d, 0xa1eef834, 0x55d02e7c, 0x92b0e066, 0x668e362e, 0x7f213a07, 0x8b1fec4f, 0xf40cd0c2, 0x0032068a, 0x199d0aa3, 0xeda3dceb, 0x2ac312f1, 0xdefdc4b9, 0xc752c890, 0x336c1ed8, 0x3974b18a, 0xcd4a67c2, 0xd4e56beb, 0x20dbbda3, 0xe7bb73b9, 0x1385a5f1, 0x0a2aa9d8, 0xfe147f90, 0x8107431d, 0x75399555, 0x6c96997c, 0x98a84f34, 0x5fc8812e, 0xabf65766, 0xb2595b4f, 0x46678d07, 0x77aa3c66, 0x8394ea2e, 0x9a3be607, 0x6e05304f, 0xa965fe55, 0x5d5b281d, 0x44f42434, 0xb0caf27c, 0xcfd9cef1, 0x3be718b9, 0x22481490, 0xd676c2d8, 0x11160cc2, 0xe528da8a, 0xfc87d6a3, 0x08b900eb, 0x02a1afb9, 0xf69f79f1, 0xef3075d8, 0x1b0ea390, 0xdc6e6d8a, 0x2850bbc2, 0x31ffb7eb, 0xc5c161a3, 0xbad25d2e, 0x4eec8b66, 0x5743874f, 0xa37d5107, 0x641d9f1d, 0x90234955, 0x898c457c, 0x7db29334, 0x9dbd1bd8, 0x6983cd90, 0x702cc1b9, 0x841217f1, 0x4372d9eb, 0xb74c0fa3, 0xaee3038a, 0x5addd5c2, 0x25cee94f, 0xd1f03f07, 0xc85f332e, 0x3c61e566, 0xfb012b7c, 0x0f3ffd34, 0x1690f11d, 0xe2ae2755, 0xe8b68807, 0x1c885e4f, 0x05275266, 0xf119842e, 0x36794a34, 0xc2479c7c, 0xdbe89055, 0x2fd6461d, 0x50c57a90, 0xa4fbacd8, 0xbd54a0f1, 0x496a76b9, 0x8e0ab8a3, 0x7a346eeb, 0x639b62c2, 0x97a5b48a },
    { 0x00000000, 0xcb567ba5, 0x934081bb, 0x5816fa1e, 0x236d7587, 0xe83b0e22, 0xb02df43c, 0x7b7b8f99, 0x46daeb0e, 0x8d8c90ab, 0xd59a6ab5, 0x1ecc1110, 0x65b79e89, 0xaee1e52c, 0xf6f71f32, 0x3da16497, 0x8db5d61c, 0x46e3adb9, 0x1ef557a7, 0xd5a32c02, 0xaed8a39b, 0x658ed83e, 0x3d982220, 0xf6ce5985, 0xcb6f3d12, 0x003946b7, 0x582fbca9, 0x9379c70c, 0xe8024895, 0x23543330, 0x7b42c92e, 0xb014b28b, 0x1e87dac9, 0xd5d1a16c, 0x8dc75b72, 0x469120d7, 0x3deaaf4e, 0xf6bcd4eb, 0xaeaa2ef5, 0x65fc5550, 0x585d31c7, 0x930b4a62, 0xcb1db07c, 0x004bcbd9, 0x7b304440, 0xb0663fe5, 0xe870c5fb, 0x2326be5e, 0x93320cd5, 0x58647770, 0x00728d6e, 0xcb24f6cb, 0xb05f7952, 0x7b0902f7, 0x231ff8e9, 0xe849834c, 0xd5e8e7db, 0x1ebe9c7e, 0x46a86660, 0x8dfe1dc5, 0xf685925c, 0x3dd3e9f9, 0x65c513e7, 0xae936842, 0x3d0fb592, 0xf659ce37, 0xae4f3429, 0x65194f8c, 0x1e62c015, 0xd534bbb0, 0x8d2241ae, 0x46743a0b, 0x7bd55e9c, 0xb0832539, 0xe895df27, 0x23c3a482, 0x58b82b1b, 0x93ee50be, 0xcbf8aaa0, 0x00aed105, 0xb0ba638e, 0x7bec182b, 0x23fae235, 0xe8ac9990, 0x93d71609, 0x58816dac, 0x009797b2, 0xcbc1ec17, 0xf6608880, 0x3d36f325, 0x6520093b, 0xae76729e, 0xd50dfd07, 0x1e5b86a2, 0x464d7cbc, 0x8d1b0719, 0x23886f5b, 0xe8de14fe, 0xb0c8eee0, 0x7b9e9545, 0x00e51adc, 0xcbb36179, 0x93a59b67, 0x58f3e0c2, 0x65528455, 0xae04fff0, 0xf61205ee, 0x3d447e4b, 0x463ff1d2, 0x8d698a77, 0xd57f7069, 0x1e290bcc, 0xae3db947, 0x656bc2e2, 0x3d7d38fc, 0xf62b4359, 0x8d50ccc0, 0x4606b765, 0x1e104d7b, 0xd54636de, 0xe8e75249, 0x23b129ec, 0x7ba7d3f2, 0xb0f1a857, 0xcb8a27ce, 0x00dc5c6b, 0x58caa675, 0x939cddd0, 0x7a1f6b24, 0xb1491081, 0xe95fea9f, 0x2209913a, 0x59721ea3, 0x92246506, 0xca329f18, 0x0164e4bd, 0x3cc5802a, 0xf793fb8f, 0xaf850191, 0x64d37a34, 0x1fa8f5ad, 0xd4fe8e08, 0x8ce87416, 0x47be0fb3, 0xf7aabd38, 0x3cfcc69d, 0x64ea3c83, 0xafbc4726, 0xd4c7c8bf, 0x1f91b31a, 0x47874904, 0x8cd132a1, 0xb1705636, 0x7a262d93, 0x2230d78d, 0xe966ac28, 0x921d23b1, 0x594b5814, 0x015da20a, 0xca0bd9af, 0x6498b1ed, 0xafceca48, 0xf7d83056, 0x3c8e4bf3, 0x47f5c46a, 0x8ca3bfcf, 0xd4b545d1, 0x1fe33e74, 0x22425ae3, 0xe9142146, 0xb102db58, 0x7a54a0fd, 0x012f2f64, 0xca7954c1, 0x926faedf, 0x5939d57a, 0xe92d67f1, 0x227b1c54, 0x7a6de64a, 0xb13b9def, 0xca401276, 0x011669d3, 0x590093cd, 0x9256e868, 0xaff78cff, 0x64a1f75a, 0x3cb70d44, 0xf7e176e1, 0x8c9af978, 0x47cc82dd, 0x1fda78c3, 0xd48c0366, 0x4710deb6, 0x8c46a513, 0xd4505f0d, 0x1f0624a8, 0x647dab31, 0xaf2bd094, 0xf73d2a8a, 0x3c6b512f, 0x01ca35b8, 0xca9c4e1d, 0x928ab403, 0x59dccfa6, 0x22a7403f, 0xe9f13b9a, 0xb1e7c184, 0x7ab1ba21, 0xcaa508aa, 0x01f3730f, 0x59e58911, 0x92b3f2b4, 0xe9c87d2d, 0x229e0688, 0x7a88fc96, 0xb1de8733, 0x8c7fe3a4, 0x47299801, 0x1f3f621f, 0xd46919ba, 0xaf129623, 0x6444ed86, 0x3c521798, 0xf7046c3d, 0x5997047f, 0x92c17fda, 0xcad785c4, 0x0181fe61, 0x7afa71f8, 0xb1ac0a5d, 0xe9baf043, 0x22ec8be6, 0x1f4def71, 0xd41b94d4, 0x8c0d6eca, 0x475b156f, 0x3c209af6, 0xf776e153, 0xaf601b4d, 0x643660e8, 0xd422d263, 0x1f74a9c6, 0x476253d8, 0x8c34287d, 0xf74fa7e4, 0x3c19dc41, 0x640f265f, 0xaf595dfa, 0x92f8396d, 0x59ae42c8, 0x01b8b8d6, 0xcaeec373, 0xb1954cea, 0x7ac3374f, 0x22d5cd51, 0xe983b6f4 },
    { 0x00000000, 0x9771f7c1, 0x2b0f9973, 0xbc7e6eb2, 0x561f32e6, 0xc16ec527, 0x7d10ab95, 0xea615c54, 0xac3e65cc, 0x3b4f920d, 0x8731fcbf, 0x10400b7e, 0xfa21572a, 0x6d50a0eb, 0xd12ece59, 0x465f3998, 0x5d90bd69, 0xcae14aa8, 0x769f241a, 0xe1eed3db, 0x0b8f8f8f, 0x9cfe784e, 0x208016fc, 0xb7f1e13d, 0xf1aed8a5, 0x66df2f64, 0xdaa141d6, 0x4dd0b617, 0xa7b1ea43, 0x30c01d82, 0x8cbe7330, 0x1bcf84f1, 0xbb217ad2, 0x2c508d13, 0x902ee3a1, 0x075f1460, 0xed3e4834, 0x7a4fbff5, 0xc631d147, 0x51402686, 0x171f1f1e, 0x806ee8df, 0x3c10866d, 0xab6171ac, 0x41002df8, 0xd671da39, 0x6a0fb48b, 0xfd7e434a, 0xe6b1c7bb, 0x71c0307a, 0xcdbe5ec8, 0x5acfa909, 0xb0aef55d, 0x27df029c, 0x9ba16c2e, 0x0cd09bef, 0x4a8fa277, 0xddfe55b6, 0x61803b04, 0xf6f1ccc5, 0x1c909091, 0x8be16750, 0x379f09e2, 0xa0eefe23, 0x73ae8355, 0xe4df7494, 0x58a11a26, 0xcfd0ede7, 0x25b1b1b3, 0xb2c04672, 0x0ebe28c0, 0x99cfdf01, 0xdf90e699, 0x48e11158, 0xf49f7fea, 0x63ee882b, 0x898fd47f, 0x1efe23be, 0xa2804d0c, 0x35f1bacd, 0x2e3e3e3c, 0xb94fc9fd, 0x0531a74f, 0x9240508e, 0x78210cda, 0xef50fb1b, 0x532e95a9, 0xc45f6268, 0x82005bf0, 0x1571ac31, 0xa90fc283, 0x3e7e3542, 0xd41f6916, 0x436e9ed7, 0xff10f065, 0x686107a4, 0xc88ff987, 0x5ffe0e46, 0xe38060f4, 0x74f19735, 0x9e90cb61, 0x09e13ca0, 0xb59f5212, 0x22eea5d3, 0x64b19c4b, 0xf3c06b8a, 0x4fbe0538, 0xd8cff2f9, 0x32aeaead, 0xa5df596c, 0x19a137de, 0x8ed0c01f, 0x951f44ee, 0x026eb32f, 0xbe10dd9d, 0x29612a5c, 0xc3007608, 0x547181c9, 0xe80fef7b, 0x7f7e18ba, 0x39212122, 0xae50d6e3, 0x122eb851, 0x855f4f90, 0x6f3e13c4, 0xf84fe405, 0x44318ab7, 0xd3407d76, 0xe75d06aa, 0x702cf16b, 0xcc529fd9, 0x5b236818, 0xb142344c, 0x2633c38d, 0x9a4dad3f, 0x0d3c5afe, 0x4b636366, 0xdc1294a7, 0x606cfa15, 0xf71d0dd4, 0x1d7c5180, 0x8a0da641, 0x3673c8f3, 0xa1023f32, 0xbacdbbc3, 0x2dbc4c02, 0x91c222b0, 0x06b3d571, 0xecd28925, 0x7ba37ee4, 0xc7dd1056, 0x50ace797, 0x16f3de0f, 0x818229ce, 0x3dfc477c, 0xaa8db0bd, 0x40ecece9, 0xd79d1b28, 0x6be3759a, 0xfc92825b, 0x5c7c7c78, 0xcb0d8bb9, 0x7773e50b, 0xe00212ca, 0x0a634e9e, 0x9d12b95f, 0x216cd7ed, 0xb61d202c, 0xf04219b4, 0x6733ee75, 0xdb4d80c7, 0x4c3c7706, 0xa65d2b52, 0x312cdc93, 0x8d52b221, 0x1a2345e0, 0x01ecc111, 0x969d36d0, 0x2ae35862, 0xbd92afa3, 0x57f3f3f7, 0xc0820436, 0x7cfc6a84, 0xeb8d9d45, 0xadd2a4dd, 0x3aa3531c, 0x86dd3dae, 0x11acca6f, 0xfbcd963b, 0x6cbc61fa, 0xd0c20f48, 0x47b3f889, 0x94f385ff, 0x0382723e, 0xbffc1c8c, 0x288deb4d, 0xc2ecb719, 0x559d40d8, 0xe9e32e6a, 0x7e92d9ab, 0x38cde033, 0xafbc17f2, 0x13c27940, 0x84b38e81, 0x6ed2d2d5, 0xf9a32514, 0x45dd4ba6, 0xd2acbc67, 0xc9633896, 0x5e12cf57, 0xe26ca1e5, 0x751d5624, 0x9f7c0a70, 0x080dfdb1, 0xb4739303, 0x230264c2, 0x655d5d5a, 0xf22caa9b, 0x4e52c429, 0xd92333e8, 0x33426fbc, 0xa433987d, 0x184df6cf, 0x8f3c010e, 0x2fd2ff2d, 0xb8a308ec, 0x04dd665e, 0x93ac919f, 0x79cdcdcb, 0xeebc3a0a, 0x52c254b8, 0xc5b3a379, 0x83ec9ae1, 0x149d6d20, 0xa8e30392, 0x3f92f453, 0xd5f3a807, 0x42825fc6, 0xfefc3174, 0x698dc6b5, 0x72424244, 0xe533b585, 0x594ddb37, 0xce3c2cf6, 0x245d70a2, 0xb32c8763, 0x0f52e9d1, 0x98231e10, 0xde7c2788, 0x490dd049, 0xf573befb, 0x6202493a, 0x8863156e, 0x1f12e2af, 0xa36c8c1d, 0x341d7bdc },
    { 0x00000000, 0x3171d430, 0x62e3a860, 0x53927c50, 0xc5c750c0, 0xf4b684f0, 0xa724f8a0, 0x96552c90, 0x8e62d771, 0xbf130341, 0xec817f11, 0xddf0ab21, 0x4ba587b1, 0x7ad45381, 0x29462fd1, 0x1837fbe1, 0x1929d813, 0x28580c23, 0x7bca7073, 0x4abba443, 0xdcee88d3, 0xed9f5ce3, 0xbe0d20b3, 0x8f7cf483, 0x974b0f62, 0xa63adb52, 0xf5a8a702, 0xc4d97332, 0x528c5fa2, 0x63fd8b92, 0x306ff7c2, 0x011e23f2, 0x3253b026, 0x03226416, 0x50b01846, 0x61c1cc76, 0xf794e0e6, 0xc6e534d6, 0x95774886, 0xa4069cb6, 0xbc316757, 0x8d40b367, 0xded2cf37, 0xefa31b07, 0x79f63797, 0x4887e3a7, 0x1b159ff7, 0x2a644bc7, 0x2b7a6835, 0x1a0bbc05, 0x4999c055, 0x78e81465, 0xeebd38f5, 0xdfccecc5, 0x8c5e9095, 0xbd2f44a5, 0xa518bf44, 0x94696b74, 0xc7fb1724, 0xf68ac314, 0x60dfef84, 0x51ae3bb4, 0x023c47e4, 0x334d93d4, 0x64a7604c, 0x55d6b47c, 0x0644c82c, 0x37351c1c, 0xa160308c, 0x9011e4bc, 0xc38398ec, 0xf2f24cdc, 0xeac5b73d, 0xdbb4630d, 0x88261f5d, 0xb957cb6d, 0x2f02e7fd, 0x1e7333cd, 0x4de14f9d, 0x7c909bad, 0x7d8eb85f, 0x4cff6c6f, 0x1f6d103f, 0x2e1cc40f, 0xb849e89f, 0x89383caf, 0xdaaa40ff, 0xebdb94cf, 0xf3ec6f2e, 0xc29dbb1e, 0x910fc74e, 0xa07e137e, 0x362b3fee, 0x075aebde, 0x54c8978e, 0x65b943be, 0x56f4d06a, 0x6785045a, 0x3417780a, 0x0566ac3a, 0x933380aa, 0xa242549a, 0xf1d028ca, 0xc0a1fcfa, 0xd896071b, 0xe9e7d32b, 0xba75af7b, 0x8b047b4b, 0x1d5157db, 0x2c2083eb, 0x7fb2ffbb, 0x4ec32b8b, 0x4fdd0879, 0x7eacdc49, 0x2d3ea019, 0x1c4f7429, 0x8a1a58b9, 0xbb6b8c89, 0xe8f9f0d9, 0xd98824e9, 0xc1bfdf08, 0xf0ce0b38, 0xa35c7768, 0x922da358, 0x04788fc8, 0x35095bf8, 0x669b27a8, 0x57eaf398, 0xc94ec098, 0xf83f14a8, 0xabad68f8, 0x9adcbcc8, 0x0c899058, 0x3df84468, 0x6e6a3838, 0x5f1bec08, 0x472c17e9, 0x765dc3d9, 0x25cfbf89, 0x14be6bb9, 0x82eb4729, 0xb39a9319, 0xe008ef49, 0xd1793b79, 0xd067188b, 0xe116ccbb, 0xb284b0eb, 0x83f564db, 0x15a0484b, 0x24d19c7b, 0x7743e02b, 0x4632341b, 0x5e05cffa, 0x6f741bca, 0x3ce6679a, 0x0d97b3aa, 0x9bc29f3a, 0xaab34b0a, 0xf921375a, 0xc850e36a, 0xfb1d70be, 0xca6ca48e, 0x99fed8de, 0xa88f0cee, 0x3eda207e, 0x0fabf44e, 0x5c39881e, 0x6d485c2e, 0x757fa7cf, 0x440e73ff, 0x179c0faf, 0x26eddb9f, 0xb0b8f70f, 0x81c9233f, 0xd25b5f6f, 0xe32a8b5f, 0xe234a8ad, 0xd3457c9d, 0x80d700cd, 0xb1a6d4fd, 0x27f3f86d, 0x16822c5d, 0x4510500d, 0x7461843d, 0x6c567fdc, 0x5d27abec, 0x0eb5d7bc, 0x3fc4038c, 0xa9912f1c, 0x98e0fb2c, 0xcb72877c, 0xfa03534c, 0xade9a0d4, 0x9c9874e4, 0xcf0a08b4, 0xfe7bdc84, 0x682ef014, 0x595f2424, 0x0acd5874, 0x3bbc8c44, 0x238b77a5, 0x12faa395, 0x4168dfc5, 0x70190bf5, 0xe64c2765, 0xd73df355, 0x84af8f05, 0xb5de5b35, 0xb4c078c7, 0x85b1acf7, 0xd623d0a7, 0xe7520497, 0x71072807, 0x4076fc37, 0x13e48067, 0x22955457, 0x3aa2afb6, 0x0bd37b86, 0x584107d6, 0x6930d3e6, 0xff65ff76, 0xce142b46, 0x9d865716, 0xacf78326, 0x9fba10f2, 0xaecbc4c2, 0xfd59b892, 0xcc286ca2, 0x5a7d4032, 0x6b0c9402, 0x389ee852, 0x09ef3c62, 0x11d8c783, 0x20a913b3, 0x733b6fe3, 0x424abbd3, 0xd41f9743, 0xe56e4373, 0xb6fc3f23, 0x878deb13, 0x8693c8e1, 0xb7e21cd1, 0xe4706081, 0xd501b4b1, 0x43549821, 0x72254c11, 0x21b73041, 0x10c6e471, 0x08f11f90, 0x3980cba0, 0x6a12b7f0, 0x5b6363c0, 0xcd364f50, 0xfc479b60, 0xafd5e730, 0x9ea43300 },
    { 0x00000000, 0x30d23865, 0x61a470ca, 0x517648af, 0xc348e194, 0xf39ad9f1, 0xa2ec915e, 0x923ea93b, 0x837db5d9, 0xb3af8dbc, 0xe2d9c513, 0xd20bfd76, 0x4035544d, 0x70e76c28, 0x21912487, 0x11431ce2, 0x03171d43, 0x33c52526, 0x62b36d89, 0x526155ec, 0xc05ffcd7, 0xf08dc4b2, 0xa1fb8c1d, 0x9129b478, 0x806aa89a, 0xb0b890ff, 0xe1ced850, 0xd11ce035, 0x4322490e, 0x73f0716b, 0x228639c4, 0x125401a1, 0x062e3a86, 0x36fc02e3, 0x678a4a4c, 0x57587229, 0xc566db12, 0xf5b4e377, 0xa4c2abd8, 0x941093bd, 0x85538f5f, 0xb581b73a, 0xe4f7ff95, 0xd425c7f0, 0x461b6ecb, 0x76c956ae, 0x27bf1e01, 0x176d2664, 0x053927c5, 0x35eb1fa0, 0x649d570f, 0x544f6f6a, 0xc671c651, 0xf6a3fe34, 0xa7d5b69b, 0x97078efe, 0x8644921c, 0xb696aa79, 0xe7e0e2d6, 0xd732dab3, 0x450c7388, 0x75de4bed, 0x24a80342, 0x147a3b27, 0x0c5c750c, 0x3c8e4d69, 0x6df805c6, 0x5d2a3da3, 0xcf149498, 0xffc6acfd, 0xaeb0e452, 0x9e62dc37, 0x8f21c0d5, 0xbff3f8b0, 0xee85b01f, 0xde57887a, 0x4c692141, 0x7cbb1924, 0x2dcd518b, 0x1d1f69ee, 0x0f4b684f, 0x3f99502a, 0x6eef1885, 0x5e3d20e0, 0xcc0389db, 0xfcd1b1be, 0xada7f911, 0x9d75c174, 0x8c36dd96, 0xbce4e5f3, 0xed92ad5c, 0xdd409539, 0x4f7e3c02, 0x7fac0467, 0x2eda4cc8, 0x1e0874ad, 0x0a724f8a, 0x3aa077ef, 0x6bd63f40, 0x5b040725, 0xc93aae1e, 0xf9e8967b, 0xa89eded4, 0x984ce6b1, 0x890ffa53, 0xb9ddc236, 0xe8ab8a99, 0xd879b2fc, 0x4a471bc7, 0x7a9523a2, 0x2be36b0d, 0x1b315368, 0x096552c9, 0x39b76aac, 0x68c12203, 0x58131a66, 0xca2db35d, 0xfaff8b38, 0xab89c397, 0x9b5bfbf2, 0x8a18e710, 0xbacadf75, 0xebbc97da, 0xdb6eafbf, 0x49500684, 0x79823ee1, 0x28f4764e, 0x18264e2b, 0x18b8ea18, 0x286ad27d, 0x791c9ad2, 0x49cea2b7, 0xdbf00b8c, 0xeb2233e9, 0xba547b46, 0x8a864323, 0x9bc55fc1, 0xab1767a4, 0xfa612f0b, 0xcab3176e, 0x588dbe55, 0x685f8630, 0x3929ce9f, 0x09fbf6fa, 0x1baff75b, 0x2b7dcf3e, 0x7a0b8791, 0x4ad9bff4, 0xd8e716cf, 0xe8352eaa, 0xb9436605, 0x89915e60, 0x98d24282, 0xa8007ae7, 0xf9763248, 0xc9a40a2d, 0x5b9aa316, 0x6b489b73, 0x3a3ed3dc, 0x0aecebb9, 0x1e96d09e, 0x2e44e8fb, 0x7f32a054, 0x4fe09831, 0xddde310a, 0xed0c096f, 0xbc7a41c0, 0x8ca879a5, 0x9deb6547, 0xad395d22, 0xfc4f158d, 0xcc9d2de8, 0x5ea384d3, 0x6e71bcb6, 0x3f07f419, 0x0fd5cc7c, 0x1d81cddd, 0x2d53f5b8, 0x7c25bd17, 0x4cf78572, 0xdec92c49, 0xee1b142c, 0xbf6d5c83, 0x8fbf64e6, 0x9efc7804, 0xae2e4061, 0xff5808ce, 0xcf8a30ab, 0x5db49990, 0x6d66a1f5, 0x3c10e95a, 0x0cc2d13f, 0x14e49f14, 0x2436a771, 0x7540efde, 0x4592d7bb, 0xd7ac7e80, 0xe77e46e5, 0xb6080e4a, 0x86da362f, 0x97992acd, 0xa74b12a8, 0xf63d5a07, 0xc6ef6262, 0x54d1cb59, 0x6403f33c, 0x3575bb93, 0x05a783f6, 0x17f38257, 0x2721ba32, 0x7657f29d, 0x4685caf8, 0xd4bb63c3, 0xe4695ba6, 0xb51f1309, 0x85cd2b6c, 0x948e378e, 0xa45c0feb, 0xf52a4744, 0xc5f87f21, 0x57c6d61a, 0x6714ee7f, 0x3662a6d0, 0x06b09eb5, 0x12caa592, 0x22189df7, 0x736ed558, 0x43bced3d, 0xd1824406, 0xe1507c63, 0xb02634cc, 0x80f40ca9, 0x91b7104b, 0xa165282e, 0xf0136081, 0xc0c158e4, 0x52fff1df, 0x622dc9ba, 0x335b8115, 0x0389b970, 0x11ddb8d1, 0x210f80b4, 0x7079c81b, 0x40abf07e, 0xd2955945, 0xe2476120, 0xb331298f, 0x83e311ea, 0x92a00d08, 0xa272356d, 0xf3047dc2, 0xc3d645a7, 0x51e8ec9c, 0x613ad4f9, 0x304c9c56, 0x009ea433 },
    { 0x00000000, 0x54075546, 0xa80eaa8c, 0xfc09ffca, 0x55f123e9, 0x01f676af, 0xfdff8965, 0xa9f8dc23, 0xabe247d2, 0xffe51294, 0x03eced5e, 0x57ebb818, 0xfe13643b, 0xaa14317d, 0x561dceb7, 0x021a9bf1, 0x5228f955, 0x062fac13, 0xfa2653d9, 0xae21069f, 0x07d9dabc, 0x53de8ffa, 0xafd77030, 0xfbd02576, 0xf9cabe87, 0xadcdebc1, 0x51c4140b, 0x05c3414d, 0xac3b9d6e, 0xf83cc828, 0x043537e2, 0x503262a4, 0xa451f2aa, 0xf056a7ec, 0x0c5f5826, 0x58580d60, 0xf1a0d143, 0xa5a78405, 0x59ae7bcf, 0x0da92e89, 0x0fb3b578, 0x5bb4e03e, 0xa7bd1ff4, 0xf3ba4ab2, 0x5a429691, 0x0e45c3d7, 0xf24c3c1d, 0xa64b695b, 0xf6790bff, 0xa27e5eb9, 0x5e77a173, 0x0a70f435, 0xa3882816, 0xf78f7d50, 0x0b86829a, 0x5f81d7dc, 0x5d9b4c2d, 0x099c196b, 0xf595e6a1, 0xa192b3e7, 0x086a6fc4, 0x5c6d3a82, 0xa064c548, 0xf463900e, 0x4d4f93a5, 0x1948c6e3, 0xe5413929, 0xb1466c6f, 0x18beb04c, 0x4cb9e50a, 0xb0b01ac0, 0xe4b74f86, 0xe6add477, 0xb2aa8131, 0x4ea37efb, 0x1aa42bbd, 0xb35cf79e, 0xe75ba2d8, 0x1b525d12, 0x4f550854, 0x1f676af0, 0x4b603fb6, 0xb769c07c, 0xe36e953a, 0x4a964919, 0x1e911c5f, 0xe298e395, 0xb69fb6d3, 0xb4852d22, 0xe0827864, 0x1c8b87ae, 0x488cd2e8, 0xe1740ecb, 0xb5735b8d, 0x497aa447, 0x1d7df101, 0xe91e610f, 0xbd193449, 0x4110cb83, 0x15179ec5, 0xbcef42e6, 0xe8e817a0, 0x14e1e86a, 0x40e6bd2c, 0x42fc26dd, 0x16fb739b, 0xeaf28c51, 0xbef5d917, 0x170d0534, 0x430a5072, 0xbf03afb8, 0xeb04fafe, 0xbb36985a, 0xef31cd1c, 0x133832d6, 0x473f6790, 0xeec7bbb3, 0xbac0eef5, 0x46c9113f, 0x12ce4479, 0x10d4df88, 0x44d38ace, 0xb8da7504, 0xecdd2042, 0x4525fc61, 0x1122a927, 0xed2b56ed, 0xb92c03ab, 0x9a9f274a, 0xce98720c, 0x32918dc6, 0x6696d880, 0xcf6e04a3, 0x9b6951e5, 0x6760ae2f, 0x3367fb69, 0x317d6098, 0x657a35de, 0x9973ca14, 0xcd749f52, 0x648c4371, 0x308b1637, 0xcc82e9fd, 0x9885bcbb, 0xc8b7de1f, 0x9cb08b59, 0x60b97493, 0x34be21d5, 0x9d46fdf6, 0xc941a8b0, 0x3548577a, 0x614f023c, 0x635599cd, 0x3752cc8b, 0xcb5b3341, 0x9f5c6607, 0x36a4ba24, 0x62a3ef62, 0x9eaa10a8, 0xcaad45ee, 0x3eced5e0, 0x6ac980a6, 0x96c07f6c, 0xc2c72a2a, 0x6b3ff609, 0x3f38a34f, 0xc3315c85, 0x973609c3, 0x952c9232, 0xc12bc774, 0x3d2238be, 0x69256df8, 0xc0ddb1db, 0x94dae49d, 0x68d31b57, 0x3cd44e11, 0x6ce62cb5, 0x38e179f3, 0xc4e88639, 0x90efd37f, 0x39170f5c, 0x6d105a1a, 0x9119a5d0, 0xc51ef096, 0xc7046b67, 0x93033e21, 0x6f0ac1eb, 0x3b0d94ad, 0x92f5488e, 0xc6f21dc8, 0x3afbe202, 0x6efcb744, 0xd7d0b4ef, 0x83d7e1a9, 0x7fde1e63, 0x2bd94b25, 0x82219706, 0xd626c240, 0x2a2f3d8a, 0x7e2868cc, 0x7c32f33d, 0x2835a67b, 0xd43c59b1, 0x803b0cf7, 0x29c3d0d4, 0x7dc48592, 0x81cd7a58, 0xd5ca2f1e, 0x85f84dba, 0xd1ff18fc, 0x2df6e736, 0x79f1b270, 0xd0096e53, 0x840e3b15, 0x7807c4df, 0x2c009199, 0x2e1a0a68, 0x7a1d5f2e, 0x8614a0e4, 0xd213f5a2, 0x7beb2981, 0x2fec7cc7, 0xd3e5830d, 0x87e2d64b, 0x73814645, 0x27861303, 0xdb8fecc9, 0x8f88b98f, 0x267065ac, 0x727730ea, 0x8e7ecf20, 0xda799a66, 0xd8630197, 0x8c6454d1, 0x706dab1b, 0x246afe5d, 0x8d92227e, 0xd9957738, 0x259c88f2, 0x719bddb4, 0x21a9bf10, 0x75aeea56, 0x89a7159c, 0xdda040da, 0x74589cf9, 0x205fc9bf, 0xdc563675, 0x88516333, 0x8a4bf8c2, 0xde4cad84, 0x2245524e, 0x76420708, 0xdfbadb2b, 0x8bbd8e6d, 0x77b471a7, 0x23b324e1 },
    { 0x00000000, 0x678efd01, 0xcf1dfa02, 0xa8930703, 0x9bd782f5, 0xfc597ff4, 0x54ca78f7, 0x334485f6, 0x3243731b, 0x55cd8e1a, 0xfd5e8919, 0x9ad07418, 0xa994f1ee, 0xce1a0cef, 0x66890bec, 0x0107f6ed, 0x6486e636, 0x03081b37, 0xab9b1c34, 0xcc15e135, 0xff5164c3, 0x98df99c2, 0x304c9ec1, 0x57c263c0, 0x56c5952d, 0x314b682c, 0x99d86f2f, 0xfe56922e, 0xcd1217d8, 0xaa9cead9, 0x020fedda, 0x658110db, 0xc90dcc6c, 0xae83316d, 0x0610366e, 0x619ecb6f, 0x52da4e99, 0x3554b398, 0x9dc7b49b, 0xfa49499a, 0xfb4ebf77, 0x9cc04276, 0x34534575, 0x53ddb874, 0x60993d82, 0x0717c083, 0xaf84c780, 0xc80a3a81, 0xad8b2a5a, 0xca05d75b, 0x6296d058, 0x05182d59, 0x365ca8af, 0x51d255ae, 0xf94152ad, 0x9ecfafac, 0x9fc85941, 0xf846a440, 0x50d5a343, 0x375b5e42, 0x041fdbb4, 0x639126b5, 0xcb0221b6, 0xac8cdcb7, 0x97f7ee29, 0xf0791328, 0x58ea142b, 0x3f64e92a, 0x0c206cdc, 0x6bae91dd, 0xc33d96de, 0xa4b36bdf, 0xa5b49d32, 0xc23a6033, 0x6aa96730, 0x0d279a31, 0x3e631fc7, 0x59ede2c6, 0xf17ee5c5, 0x96f018c4, 0xf371081f, 0x94fff51e, 0x3c6cf21d, 0x5be20f1c, 0x68a68aea, 0x0f2877eb, 0xa7bb70e8, 0xc0358de9, 0xc1327b04, 0xa6bc8605, 0x0e2f8106, 0x69a17c07, 0x5ae5f9f1, 0x3d6b04f0, 0x95f803f3, 0xf276fef2, 0x5efa2245, 0x3974df44, 0x91e7d847, 0xf6692546, 0xc52da0b0, 0xa2a35db1, 0x0a305ab2, 0x6dbea7b3, 0x6cb9515e, 0x0b37ac5f, 0xa3a4ab5c, 0xc42a565d, 0xf76ed3ab, 0x90e02eaa, 0x387329a9, 0x5ffdd4a8, 0x3a7cc473, 0x5df23972, 0xf5613e71, 0x92efc370, 0xa1ab4686, 0xc625bb87, 0x6eb6bc84, 0x09384185, 0x083fb768, 0x6fb14a69, 0xc7224d6a, 0xa0acb06b, 0x93e8359d, 0xf466c89c, 0x5cf5cf9f, 0x3b7b329e, 0x2a03aaa3, 0x4d8d57a2, 0xe51e50a1, 0x8290ada0, 0xb1d42856, 0xd65ad557, 0x7ec9d254, 0x19472f55, 0x1840d9b8, 0x7fce24b9, 0xd75d23ba, 0xb0d3debb, 0x83975b4d, 0xe419a64c, 0x4c8aa14f, 0x2b045c4e, 0x4e854c95, 0x290bb194, 0x8198b697, 0xe6164b96, 0xd552ce60, 0xb2dc3361, 0x1a4f3462, 0x7dc1c963, 0x7cc63f8e, 0x1b48c28f, 0xb3dbc58c, 0xd455388d, 0xe711bd7b, 0x809f407a, 0x280c4779, 0x4f82ba78, 0xe30e66cf, 0x84809bce, 0x2c139ccd, 0x4b9d61cc, 0x78d9e43a, 0x1f57193b, 0xb7c41e38, 0xd04ae339, 0xd14d15d4, 0xb6c3e8d5, 0x1e50efd6, 0x79de12d7, 0x4a9a9721, 0x2d146a20, 0x85876d23, 0xe2099022, 0x878880f9, 0xe0067df8, 0x48957afb, 0x2f1b87fa, 0x1c5f020c, 0x7bd1ff0d, 0xd342f80e, 0xb4cc050f, 0xb5cbf3e2, 0xd2450ee3, 0x7ad609e0, 0x1d58f4e1, 0x2e1c7117, 0x49928c16, 0xe1018b15, 0x868f7614, 0xbdf4448a, 0xda7ab98b, 0x72e9be88, 0x15674389, 0x2623c67f, 0x41ad3b7e, 0xe93e3c7d, 0x8eb0c17c, 0x8fb73791, 0xe839ca90, 0x40aacd93, 0x27243092, 0x1460b564, 0x73ee4865, 0xdb7d4f66, 0xbcf3b267, 0xd972a2bc, 0xbefc5fbd, 0x166f58be, 0x71e1a5bf, 0x42a52049, 0x252bdd48, 0x8db8da4b, 0xea36274a, 0xeb31d1a7, 0x8cbf2ca6, 0x242c2ba5, 0x43a2d6a4, 0x70e65352, 0x1768ae53, 0xbffba950, 0xd8755451, 0x74f988e6, 0x137775e7, 0xbbe472e4, 0xdc6a8fe5, 0xef2e0a13, 0x88a0f712, 0x2033f011, 0x47bd0d10, 0x46bafbfd, 0x213406fc, 0x89a701ff, 0xee29fcfe, 0xdd6d7908, 0xbae38409, 0x1270830a, 0x75fe7e0b, 0x107f6ed0, 0x77f193d1, 0xdf6294d2, 0xb8ec69d3, 0x8ba8ec25, 0xec261124, 0x44b51627, 0x233beb26, 0x223c1dcb, 0x45b2e0ca, 0xed21e7c9, 0x8aaf1ac8, 0xb9eb9f3e, 0xde65623f, 0x76f6653c, 0x1178983d },
    { 0x00000000, 0xf20c0dfe, 0xe1f46d0d, 0x13f860f3, 0xc604aceb, 0x3408a115, 0x27f0c1e6, 0xd5fccc18, 0x89e52f27, 0x7be922d9, 0x6811422a, 0x9a1d4fd4, 0x4fe183cc, 0xbded8e32, 0xae15eec1, 0x5c19e33f, 0x162628bf, 0xe42a2541, 0xf7d245b2, 0x05de484c, 0xd0228454, 0x222e89aa, 0x31d6e959, 0xc3dae4a7, 0x9fc30798, 0x6dcf0a66, 0x7e376a95, 0x8c3b676b, 0x59c7ab73, 0xabcba68d, 0xb833c67e, 0x4a3fcb80, 0x2c4c517e, 0xde405c80, 0xcdb83c73, 0x3fb4318d, 0xea48fd95, 0x1844f06b, 0x0bbc9098, 0xf9b09d66, 0xa5a97e59, 0x57a573a7, 0x445d1354, 0xb6511eaa, 0x63add2b2, 0x91a1df4c, 0x8259bfbf, 0x7055b241, 0x3a6a79c1, 0xc866743f, 0xdb9e14cc, 0x29921932, 0xfc6ed52a, 0x0e62d8d4, 0x1d9ab827, 0xef96b5d9, 0xb38f56e6, 0x41835b18, 0x527b3beb, 0xa0773615, 0x758bfa0d, 0x8787f7f3, 0x947f9700, 0x66739afe, 0x5898a2fc, 0xaa94af02, 0xb96ccff1, 0x4b60c20f, 0x9e9c0e17, 0x6c9003e9, 0x7f68631a, 0x8d646ee4, 0xd17d8ddb, 0x23718025, 0x3089e0d6, 0xc285ed28, 0x17792130, 0xe5752cce, 0xf68d4c3d, 0x048141c3, 0x4ebe8a43, 0xbcb287bd, 0xaf4ae74e, 0x5d46eab0, 0x88ba26a8, 0x7ab62b56, 0x694e4ba5, 0x9b42465b, 0xc75ba564, 0x3557a89a, 0x26afc869, 0xd4a3c597, 0x015f098f, 0xf3530471, 0xe0ab6482, 0x12a7697c, 0x74d4f382, 0x86d8fe7c, 0x95209e8f, 0x672c9371, 0xb2d05f69, 0x40dc5297, 0x53243264, 0xa1283f9a, 0xfd31dca5, 0x0f3dd15b, 0x1cc5b1a8, 0xeec9bc56, 0x3b35704e, 0xc9397db0, 0xdac11d43, 0x28cd10bd, 0x62f2db3d, 0x90fed6c3, 0x8306b630, 0x710abbce, 0xa4f677d6, 0x56fa7a28, 0x45021adb, 0xb70e1725, 0xeb17f41a, 0x191bf9e4, 0x0ae39917, 0xf8ef94e9, 0x2d1358f1, 0xdf1f550f, 0xcce735fc, 0x3eeb3802, 0xb13145f8, 0x433d4806, 0x50c528f5, 0xa2c9250b, 0x7735e913, 0x8539e4ed, 0x96c1841e, 0x64cd89e0, 0x38d46adf, 0xcad86721, 0xd92007d2, 0x2b2c0a2c, 0xfed0c634, 0x0cdccbca, 0x1f24ab39, 0xed28a6c7, 0xa7176d47, 0x551b60b9, 0x46e3004a, 0xb4ef0db4, 0x6113c1ac, 0x931fcc52, 0x80e7aca1, 0x72eba15f, 0x2ef24260, 0xdcfe4f9e, 0xcf062f6d, 0x3d0a2293, 0xe8f6ee8b, 0x1afae375, 0x09028386, 0xfb0e8e78, 0x9d7d1486, 0x6f711978, 0x7c89798b, 0x8e857475, 0x5b79b86d, 0xa975b593, 0xba8dd560, 0x4881d89e, 0x14983ba1, 0xe694365f, 0xf56c56ac, 0x07605b52, 0xd29c974a, 0x20909ab4, 0x3368fa47, 0xc164f7b9, 0x8b5b3c39, 0x795731c7, 0x6aaf5134, 0x98a35cca, 0x4d5f90d2, 0xbf539d2c, 0xacabfddf, 0x5ea7f021, 0x02be131e, 0xf0b21ee0, 0xe34a7e13, 0x114673ed, 0xc4babff5, 0x36b6b20b, 0x254ed2f8, 0xd742df06, 0xe9a9e704, 0x1ba5eafa, 0x085d8a09, 0xfa5187f7, 0x2fad4bef, 0xdda14611, 0xce5926e2, 0x3c552b1c, 0x604cc823, 0x9240c5dd, 0x81b8a52e, 0x73b4a8d0, 0xa64864c8, 0x54446936, 0x47bc09c5, 0xb5b0043b, 0xff8fcfbb, 0x0d83c245, 0x1e7ba2b6, 0xec77af48, 0x398b6350, 0xcb876eae, 0xd87f0e5d, 0x2a7303a3, 0x766ae09c, 0x8466ed62, 0x979e8d91, 0x6592806f, 0xb06e4c77, 0x42624189, 0x519a217a, 0xa3962c84, 0xc5e5b67a, 0x37e9bb84, 0x2411db77, 0xd61dd689, 0x03e11a91, 0xf1ed176f, 0xe215779c, 0x10197a62, 0x4c00995d, 0xbe0c94a3, 0xadf4f450, 0x5ff8f9ae, 0x8a0435b6, 0x78083848, 0x6bf058bb, 0x99fc5545, 0xd3c39ec5, 0x21cf933b, 0x3237f3c8, 0xc03bfe36, 0x15c7322e, 0xe7cb3fd0, 0xf4335f23, 0x063f52dd, 0x5a26b1e2, 0xa82abc1c, 0xbbd2dcef, 0x49ded111, 0x9c221d09, 0x6e2e10f7, 0x7dd67004, 0x8fda7dfa }
};

static uint32_t long_shifts[4][256] =
{
    { 0x00000000, 0xe040e0ac, 0xc56db7a9, 0x252d5705, 0x8f3719a3, 0x6f77f90f, 0x4a5aae0a, 0xaa1a4ea6, 0x1b8245b7, 0xfbc2a51b, 0xdeeff21e, 0x3eaf12b2, 0x94b55c14, 0x74f5bcb8, 0x51d8ebbd, 0xb1980b11, 0x37048b6e, 0xd7446bc2, 0xf2693cc7, 0x1229dc6b, 0xb83392cd, 0x58737261, 0x7d5e2564, 0x9d1ec5c8, 0x2c86ced9, 0xccc62e75, 0xe9eb7970, 0x09ab99dc, 0xa3b1d77a, 0x43f137d6, 0x66dc60d3, 0x869c807f, 0x6e0916dc, 0x8e49f670, 0xab64a175, 0x4b2441d9, 0xe13e0f7f, 0x017eefd3, 0x2453b8d6, 0xc413587a, 0x758b536b, 0x95cbb3c7, 0xb0e6e4c2, 0x50a6046e, 0xfabc4ac8, 0x1afcaa64, 0x3fd1fd61, 0xdf911dcd, 0x590d9db2, 0xb94d7d1e, 0x9c602a1b, 0x7c20cab7, 0xd63a8411, 0x367a64bd, 0x135733b8, 0xf317d314, 0x428fd805, 0xa2cf38a9, 0x87e26fac, 0x67a28f00, 0xcdb8c1a6, 0x2df8210a, 0x08d5760f, 0xe89596a3, 0xdc122db8, 0x3c52cd14, 0x197f9a11, 0xf93f7abd, 0x5325341b, 0xb365d4b7, 0x964883b2, 0x7608631e, 0xc790680f, 0x27d088a3, 0x02fddfa6, 0xe2bd3f0a, 0x48a771ac, 0xa8e79100, 0x8dcac605, 0x6d8a26a9, 0xeb16a6d6, 0x0b56467a, 0x2e7b117f, 0xce3bf1d3, 0x6421bf75, 0x84615fd9, 0xa14c08dc, 0x410ce870, 0xf094e361, 0x10d403cd, 0x35f954c8, 0xd5b9b464, 0x7fa3fac2, 0x9fe31a6e, 0xbace4d6b, 0x5a8eadc7, 0xb21b3b64, 0x525bdbc8, 0x77768ccd, 0x97366c61, 0x3d2c22c7, 0xdd6cc26b, 0xf841956e, 0x180175c2, 0xa9997ed3, 0x49d99e7f, 0x6cf4c97a, 0x8cb429d6, 0x26ae6770, 0xc6ee87dc, 0xe3c3d0d9, 0x03833075, 0x851fb00a, 0x655f50a6, 0x407207a3, 0xa032e70f, 0x0a28a9a9, 0xea684905, 0xcf451e00, 0x2f05feac, 0x9e9df5bd, 0x7edd1511, 0x5bf04214, 0xbbb0a2b8, 0x11aaec1e, 0xf1ea0cb2, 0xd4c75bb7, 0x3487bb1b, 0xbdc82d81, 0x5d88cd2d, 0x78a59a28, 0x98e57a84, 0x32ff3422, 0xd2bfd48e, 0xf792838b, 0x17d26327, 0xa64a6836, 0x460a889a, 0x6327df9f, 0x83673f33, 0x297d7195, 0xc93d9139, 0xec10c63c, 0x0c502690, 0x8acca6ef, 0x6a8c4643, 0x4fa11146, 0xafe1f1ea, 0x05fbbf4c, 0xe5bb5fe0, 0xc09608e5, 0x20d6e849, 0x914ee358, 0x710e03f4, 0x542354f1, 0xb463b45d, 0x1e79fafb, 0xfe391a57, 0xdb144d52, 0x3b54adfe, 0xd3c13b5d, 0x3381dbf1, 0x16ac8cf4, 0xf6ec6c58, 0x5cf622fe, 0xbcb6c252, 0x999b9557, 0x79db75fb, 0xc8437eea, 0x28039e46, 0x0d2ec943, 0xed6e29ef, 0x47746749, 0xa73487e5, 0x8219d0e0, 0x6259304c, 0xe4c5b033, 0x0485509f, 0x21a8079a, 0xc1e8e736, 0x6bf2a990, 0x8bb2493c, 0xae9f1e39, 0x4edffe95, 0xff47f584, 0x1f071528, 0x3a2a422d, 0xda6aa281, 0x7070ec27, 0x90300c8b, 0xb51d5b8e, 0x555dbb22, 0x61da0039, 0x819ae095, 0xa4b7b790, 0x44f7573c, 0xeeed199a, 0x0eadf936, 0x2b80ae33, 0xcbc04e9f, 0x7a58458e, 0x9a18a522, 0xbf35f227, 0x5f75128b, 0xf56f5c2d, 0x152fbc81, 0x3002eb84, 0xd0420b28, 0x56de8b57, 0xb69e6bfb, 0x93b33cfe, 0x73f3dc52, 0xd9e992f4, 0x39a97258, 0x1c84255d, 0xfcc4c5f1, 0x4d5ccee0, 0xad1c2e4c, 0x88317949, 0x687199e5, 0xc26bd743, 0x222b37ef, 0x070660ea, 0xe7468046, 0x0fd316e5, 0xef93f649, 0xcabea14c, 0x2afe41e0, 0x80e40f46, 0x60a4efea, 0x4589b8ef, 0xa5c95843, 0x14515352, 0xf411b3fe, 0xd13ce4fb, 0x317c0457, 0x9b664af1, 0x7b26aa5d, 0x5e0bfd58, 0xbe4b1df4, 0x38d79d8b, 0xd8977d27, 0xfdba2a22, 0x1dfaca8e, 0xb7e08428, 0x57a06484, 0x728d3381, 0x92cdd32d, 0x2355d83c, 0xc3153890, 0xe6386f95, 0x06788f39, 0xac62c19f, 0x4c222133, 0x690f7636, 0x894f969a },
    { 0x00000000, 0x7e7c2df3, 0xfcf85be6, 0x82847615, 0xfc1cc13d, 0x8260ecce, 0x00e49adb, 0x7e98b728, 0xfdd5f48b, 0x83a9d978, 0x012daf6d, 0x7f51829e, 0x01c935b6, 0x7fb51845, 0xfd316e50, 0x834d43a3, 0xfe479fe7, 0x803bb214, 0x02bfc401, 0x7cc3e9f2, 0x025b5eda, 0x7c277329, 0xfea3053c, 0x80df28cf, 0x03926b6c, 0x7dee469f, 0xff6a308a, 0x81161d79, 0xff8eaa51, 0x81f287a2, 0x0376f1b7, 0x7d0adc44, 0xf963493f, 0x871f64cc, 0x059b12d9, 0x7be73f2a, 0x057f8802, 0x7b03a5f1, 0xf987d3e4, 0x87fbfe17, 0x04b6bdb4, 0x7aca9047, 0xf84ee652, 0x8632cba1, 0xf8aa7c89, 0x86d6517a, 0x0452276f, 0x7a2e0a9c, 0x0724d6d8, 0x7958fb2b, 0xfbdc8d3e, 0x85a0a0cd, 0xfb3817e5, 0x85443a16, 0x07c04c03, 0x79bc61f0, 0xfaf12253, 0x848d0fa0, 0x060979b5, 0x78755446, 0x06ede36e, 0x7891ce9d, 0xfa15b888, 0x8469957b, 0xf72ae48f, 0x8956c97c, 0x0bd2bf69, 0x75ae929a, 0x0b3625b2, 0x754a0841, 0xf7ce7e54, 0x89b253a7, 0x0aff1004, 0x74833df7, 0xf6074be2, 0x887b6611, 0xf6e3d139, 0x889ffcca, 0x0a1b8adf, 0x7467a72c, 0x096d7b68, 0x7711569b, 0xf595208e, 0x8be90d7d, 0xf571ba55, 0x8b0d97a6, 0x0989e1b3, 0x77f5cc40, 0xf4b88fe3, 0x8ac4a210, 0x0840d405, 0x763cf9f6, 0x08a44ede, 0x76d8632d, 0xf45c1538, 0x8a2038cb, 0x0e49adb0, 0x70358043, 0xf2b1f656, 0x8ccddba5, 0xf2556c8d, 0x8c29417e, 0x0ead376b, 0x70d11a98, 0xf39c593b, 0x8de074c8, 0x0f6402dd, 0x71182f2e, 0x0f809806, 0x71fcb5f5, 0xf378c3e0, 0x8d04ee13, 0xf00e3257, 0x8e721fa4, 0x0cf669b1, 0x728a4442, 0x0c12f36a, 0x726ede99, 0xf0eaa88c, 0x8e96857f, 0x0ddbc6dc, 0x73a7eb2f, 0xf1239d3a, 0x8f5fb0c9, 0xf1c707e1, 0x8fbb2a12, 0x0d3f5c07, 0x734371f4, 0xebb9bfef, 0x95c5921c, 0x1741e409, 0x693dc9fa, 0x17a57ed2, 0x69d95321, 0xeb5d2534, 0x952108c7, 0x166c4b64, 0x68106697, 0xea941082, 0x94e83d71, 0xea708a59, 0x940ca7aa, 0x1688d1bf, 0x68f4fc4c, 0x15fe2008, 0x6b820dfb, 0xe9067bee, 0x977a561d, 0xe9e2e135, 0x979eccc6, 0x151abad3, 0x6b669720, 0xe82bd483, 0x9657f970, 0x14d38f65, 0x6aafa296, 0x143715be, 0x6a4b384d, 0xe8cf4e58, 0x96b363ab, 0x12daf6d0, 0x6ca6db23, 0xee22ad36, 0x905e80c5, 0xeec637ed, 0x90ba1a1e, 0x123e6c0b, 0x6c4241f8, 0xef0f025b, 0x91732fa8, 0x13f759bd, 0x6d8b744e, 0x1313c366, 0x6d6fee95, 0xefeb9880, 0x9197b573, 0xec9d6937, 0x92e144c4, 0x106532d1, 0x6e191f22, 0x1081a80a, 0x6efd85f9, 0xec79f3ec, 0x9205de1f, 0x11489dbc, 0x6f34b04f, 0xedb0c65a, 0x93cceba9, 0xed545c81, 0x93287172, 0x11ac0767, 0x6fd02a94, 0x1c935b60, 0x62ef7693, 0xe06b0086, 0x9e172d75, 0xe08f9a5d, 0x9ef3b7ae, 0x1c77c1bb, 0x620bec48, 0xe146afeb, 0x9f3a8218, 0x1dbef40d, 0x63c2d9fe, 0x1d5a6ed6, 0x63264325, 0xe1a23530, 0x9fde18c3, 0xe2d4c487, 0x9ca8e974, 0x1e2c9f61, 0x6050b292, 0x1ec805ba, 0x60b42849, 0xe2305e5c, 0x9c4c73af, 0x1f01300c, 0x617d1dff, 0xe3f96bea, 0x9d854619, 0xe31df131, 0x9d61dcc2, 0x1fe5aad7, 0x61998724, 0xe5f0125f, 0x9b8c3fac, 0x190849b9, 0x6774644a, 0x19ecd362, 0x6790fe91, 0xe5148884, 0x9b68a577, 0x1825e6d4, 0x6659cb27, 0xe4ddbd32, 0x9aa190c1, 0xe43927e9, 0x9a450a1a, 0x18c17c0f, 0x66bd51fc, 0x1bb78db8, 0x65cba04b, 0xe74fd65e, 0x9933fbad, 0xe7ab4c85, 0x99d76176, 0x1b531763, 0x652f3a90, 0xe6627933, 0x981e54c0, 0x1a9a22d5, 0x64e60f26, 0x1a7eb80e, 0x640295fd, 0xe686e3e8, 0x98face1b },
    { 0x00000000, 0xd29f092f, 0xa0d264af, 0x724d6d80, 0x4448bfaf, 0x96d7b680, 0xe49adb00, 0x3605d22f, 0x88917f5e, 0x5a0e7671, 0x28431bf1, 0xfadc12de, 0xccd9c0f1, 0x1e46c9de, 0x6c0ba45e, 0xbe94ad71, 0x14ce884d, 0xc6518162, 0xb41cece2, 0x6683e5cd, 0x508637e2, 0x82193ecd, 0xf054534d, 0x22cb5a62, 0x9c5ff713, 0x4ec0fe3c, 0x3c8d93bc, 0xee129a93, 0xd81748bc, 0x0a884193, 0x78c52c13, 0xaa5a253c, 0x299d109a, 0xfb0219b5, 0x894f7435, 0x5bd07d1a, 0x6dd5af35, 0xbf4aa61a, 0xcd07cb9a, 0x1f98c2b5, 0xa10c6fc4, 0x739366eb, 0x01de0b6b, 0xd3410244, 0xe544d06b, 0x37dbd944, 0x4596b4c4, 0x9709bdeb, 0x3d5398d7, 0xefcc91f8, 0x9d81fc78, 0x4f1ef557, 0x791b2778, 0xab842e57, 0xd9c943d7, 0x0b564af8, 0xb5c2e789, 0x675deea6, 0x15108326, 0xc78f8a09, 0xf18a5826, 0x23155109, 0x51583c89, 0x83c735a6, 0x533a2134, 0x81a5281b, 0xf3e8459b, 0x21774cb4, 0x17729e9b, 0xc5ed97b4, 0xb7a0fa34, 0x653ff31b, 0xdbab5e6a, 0x09345745, 0x7b793ac5, 0xa9e633ea, 0x9fe3e1c5, 0x4d7ce8ea, 0x3f31856a, 0xedae8c45, 0x47f4a979, 0x956ba056, 0xe726cdd6, 0x35b9c4f9, 0x03bc16d6, 0xd1231ff9, 0xa36e7279, 0x71f17b56, 0xcf65d627, 0x1dfadf08, 0x6fb7b288, 0xbd28bba7, 0x8b2d6988, 0x59b260a7, 0x2bff0d27, 0xf9600408, 0x7aa731ae, 0xa8383881, 0xda755501, 0x08ea5c2e, 0x3eef8e01, 0xec70872e, 0x9e3deaae, 0x4ca2e381, 0xf2364ef0, 0x20a947df, 0x52e42a5f, 0x807b2370, 0xb67ef15f, 0x64e1f870, 0x16ac95f0, 0xc4339cdf, 0x6e69b9e3, 0xbcf6b0cc, 0xcebbdd4c, 0x1c24d463, 0x2a21064c, 0xf8be0f63, 0x8af362e3, 0x586c6bcc, 0xe6f8c6bd, 0x3467cf92, 0x462aa212, 0x94b5ab3d, 0xa2b07912, 0x702f703d, 0x02621dbd, 0xd0fd1492, 0xa6744268, 0x74eb4b47, 0x06a626c7, 0xd4392fe8, 0xe23cfdc7, 0x30a3f4e8, 0x42ee9968, 0x90719047, 0x2ee53d36, 0xfc7a3419, 0x8e375999, 0x5ca850b6, 0x6aad8299, 0xb8328bb6, 0xca7fe636, 0x18e0ef19, 0xb2baca25, 0x6025c30a, 0x1268ae8a, 0xc0f7a7a5, 0xf6f2758a, 0x246d7ca5, 0x56201125, 0x84bf180a, 0x3a2bb57b, 0xe8b4bc54, 0x9af9d1d4, 0x4866d8fb, 0x7e630ad4, 0xacfc03fb, 0xdeb16e7b, 0x0c2e6754, 0x8fe952f2, 0x5d765bdd, 0x2f3b365d, 0xfda43f72, 0xcba1ed5d, 0x193ee472, 0x6b7389f2, 0xb9ec80dd, 0x07782dac, 0xd5e72483, 0xa7aa4903, 0x7535402c, 0x43309203, 0x91af9b2c, 0xe3e2f6ac, 0x317dff83, 0x9b27dabf, 0x49b8d390, 0x3bf5be10, 0xe96ab73f, 0xdf6f6510, 0x0df06c3f, 0x7fbd01bf, 0xad220890, 0x13b6a5e1, 0xc129acce, 0xb364c14e, 0x61fbc861, 0x57fe1a4e, 0x85611361, 0xf72c7ee1, 0x25b377ce, 0xf54e635c, 0x27d16a73, 0x559c07f3, 0x87030edc, 0xb106dcf3, 0x6399d5dc, 0x11d4b85c, 0xc34bb173, 0x7ddf1c02, 0xaf40152d, 0xdd0d78ad, 0x0f927182, 0x3997a3ad, 0xeb08aa82, 0x9945c702, 0x4bdace2d, 0xe180eb11, 0x331fe23e, 0x41528fbe, 0x93cd8691, 0xa5c854be, 0x77575d91, 0x051a3011, 0xd785393e, 0x6911944f, 0xbb8e9d60, 0xc9c3f0e0, 0x1b5cf9cf, 0x2d592be0, 0xffc622cf, 0x8d8b4f4f, 0x5f144660, 0xdcd373c6, 0x0e4c7ae9, 0x7c011769, 0xae9e1e46, 0x989bcc69, 0x4a04c546, 0x3849a8c6, 0xead6a1e9, 0x54420c98, 0x86dd05b7, 0xf4906837, 0x260f6118, 0x100ab337, 0xc295ba18, 0xb0d8d798, 0x6247deb7, 0xc81dfb8b, 0x1a82f2a4, 0x68cf9f24, 0xba50960b, 0x8c554424, 0x5eca4d0b, 0x2c87208b, 0xfe1829a4, 0x408c84d5, 0x92138dfa, 0xe05ee07a, 0x32c1e955, 0x04c43b7a, 0xd65b3255, 0xa4165fd5, 0x768956fa },
    { 0x00000000, 0x4904f221, 0x9209e442, 0xdb0d1663, 0x21ffbe75, 0x68fb4c54, 0xb3f65a37, 0xfaf2a816, 0x43ff7cea, 0x0afb8ecb, 0xd1f698a8, 0x98f26a89, 0x6200c29f, 0x2b0430be, 0xf00926dd, 0xb90dd4fc, 0x87fef9d4, 0xcefa0bf5, 0x15f71d96, 0x5cf3efb7, 0xa60147a1, 0xef05b580, 0x3408a3e3, 0x7d0c51c2, 0xc401853e, 0x8d05771f, 0x5608617c, 0x1f0c935d, 0xe5fe3b4b, 0xacfac96a, 0x77f7df09, 0x3ef32d28, 0x0a118559, 0x43157778, 0x9818611b, 0xd11c933a, 0x2bee3b2c, 0x62eac90d, 0xb9e7df6e, 0xf0e32d4f, 0x49eef9b3, 0x00ea0b92, 0xdbe71df1, 0x92e3efd0, 0x681147c6, 0x2115b5e7, 0xfa18a384, 0xb31c51a5, 0x8def7c8d, 0xc4eb8eac, 0x1fe698cf, 0x56e26aee, 0xac10c2f8, 0xe51430d9, 0x3e1926ba, 0x771dd49b, 0xce100067, 0x8714f246, 0x5c19e425, 0x151d1604, 0xefefbe12, 0xa6eb4c33, 0x7de65a50, 0x34e2a871, 0x14230ab2, 0x5d27f893, 0x862aeef0, 0xcf2e1cd1, 0x35dcb4c7, 0x7cd846e6, 0xa7d55085, 0xeed1a2a4, 0x57dc7658, 0x1ed88479, 0xc5d5921a, 0x8cd1603b, 0x7623c82d, 0x3f273a0c, 0xe42a2c6f, 0xad2ede4e, 0x93ddf366, 0xdad90147, 0x01d41724, 0x48d0e505, 0xb2224d13, 0xfb26bf32, 0x202ba951, 0x692f5b70, 0xd0228f8c, 0x99267dad, 0x422b6bce, 0x0b2f99ef, 0xf1dd31f9, 0xb8d9c3d8, 0x63d4d5bb, 0x2ad0279a, 0x1e328feb, 0x57367dca, 0x8c3b6ba9, 0xc53f9988, 0x3fcd319e, 0x76c9c3bf, 0xadc4d5dc, 0xe4c027fd, 0x5dcdf301, 0x14c90120, 0xcfc41743, 0x86c0e562, 0x7c324d74, 0x3536bf55, 0xee3ba936, 0xa73f5b17, 0x99cc763f, 0xd0c8841e, 0x0bc5927d, 0x42c1605c, 0xb833c84a, 0xf1373a6b, 0x2a3a2c08, 0x633ede29, 0xda330ad5, 0x9337f8f4, 0x483aee97, 0x013e1cb6, 0xfbccb4a0, 0xb2c84681, 0x69c550e2, 0x20c1a2c3, 0x28461564, 0x6142e745, 0xba4ff126, 0xf34b0307, 0x09b9ab11, 0x40bd5930, 0x9bb04f53, 0xd2b4bd72, 0x6bb9698e, 0x22bd9baf, 0xf9b08dcc, 0xb0b47fed, 0x4a46d7fb, 0x034225da, 0xd84f33b9, 0x914bc198, 0xafb8ecb0, 0xe6bc1e91, 0x3db108f2, 0x74b5fad3, 0x8e4752c5, 0xc743a0e4, 0x1c4eb687, 0x554a44a6, 0xec47905a, 0xa543627b, 0x7e4e7418, 0x374a8639, 0xcdb82e2f, 0x84bcdc0e, 0x5fb1ca6d, 0x16b5384c, 0x2257903d, 0x6b53621c, 0xb05e747f, 0xf95a865e, 0x03a82e48, 0x4aacdc69, 0x91a1ca0a, 0xd8a5382b, 0x61a8ecd7, 0x28ac1ef6, 0xf3a10895, 0xbaa5fab4, 0x405752a2, 0x0953a083, 0xd25eb6e0, 0x9b5a44c1, 0xa5a969e9, 0xecad9bc8, 0x37a08dab, 0x7ea47f8a, 0x8456d79c, 0xcd5225bd, 0x165f33de, 0x5f5bc1ff, 0xe6561503, 0xaf52e722, 0x745ff141, 0x3d5b0360, 0xc7a9ab76, 0x8ead5957, 0x55a04f34, 0x1ca4bd15, 0x3c651fd6, 0x7561edf7, 0xae6cfb94, 0xe76809b5, 0x1d9aa1a3, 0x549e5382, 0x8f9345e1, 0xc697b7c0, 0x7f9a633c, 0x369e911d, 0xed93877e, 0xa497755f, 0x5e65dd49, 0x17612f68, 0xcc6c390b, 0x8568cb2a, 0xbb9be602, 0xf29f1423, 0x29920240, 0x6096f061, 0x9a645877, 0xd360aa56, 0x086dbc35, 0x41694e14, 0xf8649ae8, 0xb16068c9, 0x6a6d7eaa, 0x23698c8b, 0xd99b249d, 0x909fd6bc, 0x4b92c0df, 0x029632fe, 0x36749a8f, 0x7f7068ae, 0xa47d7ecd, 0xed798cec, 0x178b24fa, 0x5e8fd6db, 0x8582c0b8, 0xcc863299, 0x758be665, 0x3c8f1444, 0xe7820227, 0xae86f006, 0x54745810, 0x1d70aa31, 0xc67dbc52, 0x8f794e73, 0xb18a635b, 0xf88e917a, 0x23838719, 0x6a877538, 0x9075dd2e, 0xd9712f0f, 0x027c396c, 0x4b78cb4d, 0xf2751fb1, 0xbb71ed90, 0x607cfbf3, 0x297809d2, 0xd38aa1c4, 0x9a8e53e5, 0x41834586, 0x0887b7a7 }
};

static uint32_t short_shifts[4][256] =
{
    { 0x00000000, 0xdcb17aa4, 0xbc8e83b9, 0x603ff91d, 0x7cf17183, 0xa0400b27, 0xc07ff23a, 0x1cce889e, 0xf9e2e306, 0x255399a2, 0x456c60bf, 0x99dd1a1b, 0x85139285, 0x59a2e821, 0x399d113c, 0xe52c6b98, 0xf629b0fd, 0x2a98ca59, 0x4aa73344, 0x961649e0, 0x8ad8c17e, 0x5669bbda, 0x365642c7, 0xeae73863, 0x0fcb53fb, 0xd37a295f, 0xb345d042, 0x6ff4aae6, 0x733a2278, 0xaf8b58dc, 0xcfb4a1c1, 0x1305db65, 0xe9bf170b, 0x350e6daf, 0x553194b2, 0x8980ee16, 0x954e6688, 0x49ff1c2c, 0x29c0e531, 0xf5719f95, 0x105df40d, 0xccec8ea9, 0xacd377b4, 0x70620d10, 0x6cac858e, 0xb01dff2a, 0xd0220637, 0x0c937c93, 0x1f96a7f6, 0xc327dd52, 0xa318244f, 0x7fa95eeb, 0x6367d675, 0xbfd6acd1, 0xdfe955cc, 0x03582f68, 0xe67444f0, 0x3ac53e54, 0x5afac749, 0x864bbded, 0x9a853573, 0x46344fd7, 0x260bb6ca, 0xfabacc6e, 0xd69258e7, 0x0a232243, 0x6a1cdb5e, 0xb6ada1fa, 0xaa632964, 0x76d253c0, 0x16edaadd, 0xca5cd079, 0x2f70bbe1, 0xf3c1c145, 0x93fe3858, 0x4f4f42fc, 0x5381ca62, 0x8f30b0c6, 0xef0f49db, 0x33be337f, 0x20bbe81a, 0xfc0a92be, 0x9c356ba3, 0x40841107, 0x5c4a9999, 0x80fbe33d, 0xe0c41a20, 0x3c756084, 0xd9590b1c, 0x05e871b8, 0x65d788a5, 0xb966f201, 0xa5a87a9f, 0x7919003b, 0x1926f926, 0xc5978382, 0x3f2d4fec, 0xe39c3548, 0x83a3cc55, 0x5f12b6f1, 0x43dc3e6f, 0x9f6d44cb, 0xff52bdd6, 0x23e3c772, 0xc6cfacea, 0x1a7ed64e, 0x7a412f53, 0xa6f055f7, 0xba3edd69, 0x668fa7cd, 0x06b05ed0, 0xda012474, 0xc904ff11, 0x15b585b5, 0x758a7ca8, 0xa93b060c, 0xb5f58e92, 0x6944f436, 0x097b0d2b, 0xd5ca778f, 0x30e61c17, 0xec5766b3, 0x8c689fae, 0x50d9e50a, 0x4c176d94, 0x90a61730, 0xf099ee2d, 0x2c289489, 0xa8c8c73f, 0x7479bd9b, 0x14464486, 0xc8f73e22, 0xd439b6bc, 0x0888cc18, 0x68b73505, 0xb4064fa1, 0x512a2439, 0x8d9b5e9d, 0xeda4a780, 0x3115dd24, 0x2ddb55ba, 0xf16a2f1e, 0x9155d603, 0x4de4aca7, 0x5ee177c2, 0x82500d66, 0xe26ff47b, 0x3ede8edf, 0x22100641, 0xfea17ce5, 0x9e9e85f8, 0x422fff5c, 0xa70394c4, 0x7bb2ee60, 0x1b8d177d, 0xc73c6dd9, 0xdbf2e547, 0x07439fe3, 0x677c66fe, 0xbbcd1c5a, 0x4177d034, 0x9dc6aa90, 0xfdf9538d, 0x21482929, 0x3d86a1b7, 0xe137db13, 0x8108220e, 0x5db958aa, 0xb8953332, 0x64244996, 0x041bb08b, 0xd8aaca2f, 0xc46442b1, 0x18d53815, 0x78eac108, 0xa45bbbac, 0xb75e60c9, 0x6bef1a6d, 0x0bd0e370, 0xd76199d4, 0xcbaf114a, 0x171e6bee, 0x772192f3, 0xab90e857, 0x4ebc83cf, 0x920df96b, 0xf2320076, 0x2e837ad2, 0x324df24c, 0xeefc88e8, 0x8ec371f5, 0x52720b51, 0x7e5a9fd8, 0xa2ebe57c, 0xc2d41c61, 0x1e6566c5, 0x02abee5b, 0xde1a94ff, 0xbe256de2, 0x62941746, 0x87b87cde, 0x5b09067a, 0x3b36ff67, 0xe78785c3, 0xfb490d5d, 0x27f877f9, 0x47c78ee4, 0x9b76f440, 0x88732f25, 0x54c25581, 0x34fdac9c, 0xe84cd638, 0xf4825ea6, 0x28332402, 0x480cdd1f, 0x94bda7bb, 0x7191cc23, 0xad20b687, 0xcd1f4f9a, 0x11ae353e, 0x0d60bda0, 0xd1d1c704, 0xb1ee3e19, 0x6d5f44bd, 0x97e588d3, 0x4b54f277, 0x2b6b0b6a, 0xf7da71ce, 0xeb14f950, 0x37a583f4, 0x579a7ae9, 0x8b2b004d, 0x6e076bd5, 0xb2b61171, 0xd289e86c, 0x0e3892c8, 0x12f61a56, 0xce4760f2, 0xae7899ef, 0x72c9e34b, 0x61cc382e, 0xbd7d428a, 0xdd42bb97, 0x01f3c133, 0x1d3d49ad, 0xc18c3309, 0xa1b3ca14, 0x7d02b0b0, 0x982edb28, 0x449fa18c, 0x24a05891, 0xf8112235, 0xe4dfaaab, 0x386ed00f, 0x58512912, 0x84e053b6 },
    { 0x00000000, 0x547df88f, 0xa8fbf11e, 0xfc860991, 0x541b94cd, 0x00666c42, 0xfce065d3, 0xa89d9d5c, 0xa837299a, 0xfc4ad115, 0x00ccd884, 0x54b1200b, 0xfc2cbd57, 0xa85145d8, 0x54d74c49, 0x00aab4c6, 0x558225c5, 0x01ffdd4a, 0xfd79d4db, 0xa9042c54, 0x0199b108, 0x55e44987, 0xa9624016, 0xfd1fb899, 0xfdb50c5f, 0xa9c8f4d0, 0x554efd41, 0x013305ce, 0xa9ae9892, 0xfdd3601d, 0x0155698c, 0x55289103, 0xab044b8a, 0xff79b305, 0x03ffba94, 0x5782421b, 0xff1fdf47, 0xab6227c8, 0x57e42e59, 0x0399d6d6, 0x03336210, 0x574e9a9f, 0xabc8930e, 0xffb56b81, 0x5728f6dd, 0x03550e52, 0xffd307c3, 0xabaeff4c, 0xfe866e4f, 0xaafb96c0, 0x567d9f51, 0x020067de, 0xaa9dfa82, 0xfee0020d, 0x02660b9c, 0x561bf313, 0x56b147d5, 0x02ccbf5a, 0xfe4ab6cb, 0xaa374e44, 0x02aad318, 0x56d72b97, 0xaa512206, 0xfe2cda89, 0x53e4e1e5, 0x0799196a, 0xfb1f10fb, 0xaf62e874, 0x07ff7528, 0x53828da7, 0xaf048436, 0xfb797cb9, 0xfbd3c87f, 0xafae30f0, 0x53283961, 0x0755c1ee, 0xafc85cb2, 0xfbb5a43d, 0x0733adac, 0x534e5523, 0x0666c420, 0x521b3caf, 0xae9d353e, 0xfae0cdb1, 0x527d50ed, 0x0600a862, 0xfa86a1f3, 0xaefb597c, 0xae51edba, 0xfa2c1535, 0x06aa1ca4, 0x52d7e42b, 0xfa4a7977, 0xae3781f8, 0x52b18869, 0x06cc70e6, 0xf8e0aa6f, 0xac9d52e0, 0x501b5b71, 0x0466a3fe, 0xacfb3ea2, 0xf886c62d, 0x0400cfbc, 0x507d3733, 0x50d783f5, 0x04aa7b7a, 0xf82c72eb, 0xac518a64, 0x04cc1738, 0x50b1efb7, 0xac37e626, 0xf84a1ea9, 0xad628faa, 0xf91f7725, 0x05997eb4, 0x51e4863b, 0xf9791b67, 0xad04e3e8, 0x5182ea79, 0x05ff12f6, 0x0555a630, 0x51285ebf, 0xadae572e, 0xf9d3afa1, 0x514e32fd, 0x0533ca72, 0xf9b5c3e3, 0xadc83b6c, 0xa7c9c3ca, 0xf3b43b45, 0x0f3232d4, 0x5b4fca5b, 0xf3d25707, 0xa7afaf88, 0x5b29a619, 0x0f545e96, 0x0ffeea50, 0x5b8312df, 0xa7051b4e, 0xf378e3c1, 0x5be57e9d, 0x0f988612, 0xf31e8f83, 0xa763770c, 0xf24be60f, 0xa6361e80, 0x5ab01711, 0x0ecdef9e, 0xa65072c2, 0xf22d8a4d, 0x0eab83dc, 0x5ad67b53, 0x5a7ccf95, 0x0e01371a, 0xf2873e8b, 0xa6fac604, 0x0e675b58, 0x5a1aa3d7, 0xa69caa46, 0xf2e152c9, 0x0ccd8840, 0x58b070cf, 0xa436795e, 0xf04b81d1, 0x58d61c8d, 0x0cabe402, 0xf02ded93, 0xa450151c, 0xa4faa1da, 0xf0875955, 0x0c0150c4, 0x587ca84b, 0xf0e13517, 0xa49ccd98, 0x581ac409, 0x0c673c86, 0x594fad85, 0x0d32550a, 0xf1b45c9b, 0xa5c9a414, 0x0d543948, 0x5929c1c7, 0xa5afc856, 0xf1d230d9, 0xf178841f, 0xa5057c90, 0x59837501, 0x0dfe8d8e, 0xa56310d2, 0xf11ee85d, 0x0d98e1cc, 0x59e51943, 0xf42d222f, 0xa050daa0, 0x5cd6d331, 0x08ab2bbe, 0xa036b6e2, 0xf44b4e6d, 0x08cd47fc, 0x5cb0bf73, 0x5c1a0bb5, 0x0867f33a, 0xf4e1faab, 0xa09c0224, 0x08019f78, 0x5c7c67f7, 0xa0fa6e66, 0xf48796e9, 0xa1af07ea, 0xf5d2ff65, 0x0954f6f4, 0x5d290e7b, 0xf5b49327, 0xa1c96ba8, 0x5d4f6239, 0x09329ab6, 0x09982e70, 0x5de5d6ff, 0xa163df6e, 0xf51e27e1, 0x5d83babd, 0x09fe4232, 0xf5784ba3, 0xa105b32c, 0x5f2969a5, 0x0b54912a, 0xf7d298bb, 0xa3af6034, 0x0b32fd68, 0x5f4f05e7, 0xa3c90c76, 0xf7b4f4f9, 0xf71e403f, 0xa363b8b0, 0x5fe5b121, 0x0b9849ae, 0xa305d4f2, 0xf7782c7d, 0x0bfe25ec, 0x5f83dd63, 0x0aab4c60, 0x5ed6b4ef, 0xa250bd7e, 0xf62d45f1, 0x5eb0d8ad, 0x0acd2022, 0xf64b29b3, 0xa236d13c, 0xa29c65fa, 0xf6e19d75, 0x0a6794e4, 0x5e1a6c6b, 0xf687f137, 0xa2fa09b8, 0x5e7c0029, 0x0a01f8a6 },
    { 0x00000000, 0x4a7ff165, 0x94ffe2ca, 0xde8013af, 0x2c13b365, 0x666c4200, 0xb8ec51af, 0xf293a0ca, 0x582766ca, 0x125897af, 0xccd88400, 0x86a77565, 0x7434d5af, 0x3e4b24ca, 0xe0cb3765, 0xaab4c600, 0xb04ecd94, 0xfa313cf1, 0x24b12f5e, 0x6ecede3b, 0x9c5d7ef1, 0xd6228f94, 0x08a29c3b, 0x42dd6d5e, 0xe869ab5e, 0xa2165a3b, 0x7c964994, 0x36e9b8f1, 0xc47a183b, 0x8e05e95e, 0x5085faf1, 0x1afa0b94, 0x6571edd9, 0x2f0e1cbc, 0xf18e0f13, 0xbbf1fe76, 0x49625ebc, 0x031dafd9, 0xdd9dbc76, 0x97e24d13, 0x3d568b13, 0x77297a76, 0xa9a969d9, 0xe3d698bc, 0x11453876, 0x5b3ac913, 0x85badabc, 0xcfc52bd9, 0xd53f204d, 0x9f40d128, 0x41c0c287, 0x0bbf33e2, 0xf92c9328, 0xb353624d, 0x6dd371e2, 0x27ac8087, 0x8d184687, 0xc767b7e2, 0x19e7a44d, 0x53985528, 0xa10bf5e2, 0xeb740487, 0x35f41728, 0x7f8be64d, 0xcae3dbb2, 0x809c2ad7, 0x5e1c3978, 0x1463c81d, 0xe6f068d7, 0xac8f99b2, 0x720f8a1d, 0x38707b78, 0x92c4bd78, 0xd8bb4c1d, 0x063b5fb2, 0x4c44aed7, 0xbed70e1d, 0xf4a8ff78, 0x2a28ecd7, 0x60571db2, 0x7aad1626, 0x30d2e743, 0xee52f4ec, 0xa42d0589, 0x56bea543, 0x1cc15426, 0xc2414789, 0x883eb6ec, 0x228a70ec, 0x68f58189, 0xb6759226, 0xfc0a6343, 0x0e99c389, 0x44e632ec, 0x9a662143, 0xd019d026, 0xaf92366b, 0xe5edc70e, 0x3b6dd4a1, 0x711225c4, 0x8381850e, 0xc9fe746b, 0x177e67c4, 0x5d0196a1, 0xf7b550a1, 0xbdcaa1c4, 0x634ab26b, 0x2935430e, 0xdba6e3c4, 0x91d912a1, 0x4f59010e, 0x0526f06b, 0x1fdcfbff, 0x55a30a9a, 0x8b231935, 0xc15ce850, 0x33cf489a, 0x79b0b9ff, 0xa730aa50, 0xed4f5b35, 0x47fb9d35, 0x0d846c50, 0xd3047fff, 0x997b8e9a, 0x6be82e50, 0x2197df35, 0xff17cc9a, 0xb5683dff, 0x902bc195, 0xda5430f0, 0x04d4235f, 0x4eabd23a, 0xbc3872f0, 0xf6478395, 0x28c7903a, 0x62b8615f, 0xc80ca75f, 0x8273563a, 0x5cf34595, 0x168cb4f0, 0xe41f143a, 0xae60e55f, 0x70e0f6f0, 0x3a9f0795, 0x20650c01, 0x6a1afd64, 0xb49aeecb, 0xfee51fae, 0x0c76bf64, 0x46094e01, 0x98895dae, 0xd2f6accb, 0x78426acb, 0x323d9bae, 0xecbd8801, 0xa6c27964, 0x5451d9ae, 0x1e2e28cb, 0xc0ae3b64, 0x8ad1ca01, 0xf55a2c4c, 0xbf25dd29, 0x61a5ce86, 0x2bda3fe3, 0xd9499f29, 0x93366e4c, 0x4db67de3, 0x07c98c86, 0xad7d4a86, 0xe702bbe3, 0x3982a84c, 0x73fd5929, 0x816ef9e3, 0xcb110886, 0x15911b29, 0x5feeea4c, 0x4514e1d8, 0x0f6b10bd, 0xd1eb0312, 0x9b94f277, 0x690752bd, 0x2378a3d8, 0xfdf8b077, 0xb7874112, 0x1d338712, 0x574c7677, 0x89cc65d8, 0xc3b394bd, 0x31203477, 0x7b5fc512, 0xa5dfd6bd, 0xefa027d8, 0x5ac81a27, 0x10b7eb42, 0xce37f8ed, 0x84480988, 0x76dba942, 0x3ca45827, 0xe2244b88, 0xa85bbaed, 0x02ef7ced, 0x48908d88, 0x96109e27, 0xdc6f6f42, 0x2efccf88, 0x64833eed, 0xba032d42, 0xf07cdc27, 0xea86d7b3, 0xa0f926d6, 0x7e793579, 0x3406c41c, 0xc69564d6, 0x8cea95b3, 0x526a861c, 0x18157779, 0xb2a1b179, 0xf8de401c, 0x265e53b3, 0x6c21a2d6, 0x9eb2021c, 0xd4cdf379, 0x0a4de0d6, 0x403211b3, 0x3fb9f7fe, 0x75c6069b, 0xab461534, 0xe139e451, 0x13aa449b, 0x59d5b5fe, 0x8755a651, 0xcd2a5734, 0x679e9134, 0x2de16051, 0xf36173fe, 0xb91e829b, 0x4b8d2251, 0x01f2d334, 0xdf72c09b, 0x950d31fe, 0x8ff73a6a, 0xc588cb0f, 0x1b08d8a0, 0x517729c5, 0xa3e4890f, 0xe99b786a, 0x371b6bc5, 0x7d649aa0, 0xd7d05ca0, 0x9dafadc5, 0x432fbe6a, 0x09504f0f, 0xfbc3efc5, 0xb1bc1ea0, 0x6f3c0d0f, 0x2543fc6a },
    { 0x00000000, 0x25bbf5db, 0x4b77ebb6, 0x6ecc1e6d, 0x96efd76c, 0xb35422b7, 0xdd983cda, 0xf823c901, 0x2833d829, 0x0d882df2, 0x6344339f, 0x46ffc644, 0xbedc0f45, 0x9b67fa9e, 0xf5abe4f3, 0xd0101128, 0x5067b052, 0x75dc4589, 0x1b105be4, 0x3eabae3f, 0xc688673e, 0xe33392e5, 0x8dff8c88, 0xa8447953, 0x7854687b, 0x5def9da0, 0x332383cd, 0x16987616, 0xeebbbf17, 0xcb004acc, 0xa5cc54a1, 0x8077a17a, 0xa0cf60a4, 0x8574957f, 0xebb88b12, 0xce037ec9, 0x3620b7c8, 0x139b4213, 0x7d575c7e, 0x58eca9a5, 0x88fcb88d, 0xad474d56, 0xc38b533b, 0xe630a6e0, 0x1e136fe1, 0x3ba89a3a, 0x55648457, 0x70df718c, 0xf0a8d0f6, 0xd513252d, 0xbbdf3b40, 0x9e64ce9b, 0x6647079a, 0x43fcf241, 0x2d30ec2c, 0x088b19f7, 0xd89b08df, 0xfd20fd04, 0x93ece369, 0xb65716b2, 0x4e74dfb3, 0x6bcf2a68, 0x05033405, 0x20b8c1de, 0x4472b7b9, 0x61c94262, 0x0f055c0f, 0x2abea9d4, 0xd29d60d5, 0xf726950e, 0x99ea8b63, 0xbc517eb8, 0x6c416f90, 0x49fa9a4b, 0x27368426, 0x028d71fd, 0xfaaeb8fc, 0xdf154d27, 0xb1d9534a, 0x9462a691, 0x141507eb, 0x31aef230, 0x5f62ec5d, 0x7ad91986, 0x82fad087, 0xa741255c, 0xc98d3b31, 0xec36ceea, 0x3c26dfc2, 0x199d2a19, 0x77513474, 0x52eac1af, 0xaac908ae, 0x8f72fd75, 0xe1bee318, 0xc40516c3, 0xe4bdd71d, 0xc10622c6, 0xafca3cab, 0x8a71c970, 0x72520071, 0x57e9f5aa, 0x3925ebc7, 0x1c9e1e1c, 0xcc8e0f34, 0xe935faef, 0x87f9e482, 0xa2421159, 0x5a61d858, 0x7fda2d83, 0x111633ee, 0x34adc635, 0xb4da674f, 0x91619294, 0xffad8cf9, 0xda167922, 0x2235b023, 0x078e45f8, 0x69425b95, 0x4cf9ae4e, 0x9ce9bf66, 0xb9524abd, 0xd79e54d0, 0xf225a10b, 0x0a06680a, 0x2fbd9dd1, 0x417183bc, 0x64ca7667, 0x88e56f72, 0xad5e9aa9, 0xc39284c4, 0xe629711f, 0x1e0ab81e, 0x3bb14dc5, 0x557d53a8, 0x70c6a673, 0xa0d6b75b, 0x856d4280, 0xeba15ced, 0xce1aa936, 0x36396037, 0x138295ec, 0x7d4e8b81, 0x58f57e5a, 0xd882df20, 0xfd392afb, 0x93f53496, 0xb64ec14d, 0x4e6d084c, 0x6bd6fd97, 0x051ae3fa, 0x20a11621, 0xf0b10709, 0xd50af2d2, 0xbbc6ecbf, 0x9e7d1964, 0x665ed065, 0x43e525be, 0x2d293bd3, 0x0892ce08, 0x282a0fd6, 0x0d91fa0d, 0x635de460, 0x46e611bb, 0xbec5d8ba, 0x9b7e2d61, 0xf5b2330c, 0xd009c6d7, 0x0019d7ff, 0x25a22224, 0x4b6e3c49, 0x6ed5c992, 0x96f60093, 0xb34df548, 0xdd81eb25, 0xf83a1efe, 0x784dbf84, 0x5df64a5f, 0x333a5432, 0x1681a1e9, 0xeea268e8, 0xcb199d33, 0xa5d5835e, 0x806e7685, 0x507e67ad, 0x75c59276, 0x1b098c1b, 0x3eb279c0, 0xc691b0c1, 0xe32a451a, 0x8de65b77, 0xa85daeac, 0xcc97d8cb, 0xe92c2d10, 0x87e0337d, 0xa25bc6a6, 0x5a780fa7, 0x7fc3fa7c, 0x110fe411, 0x34b411ca, 0xe4a400e2, 0xc11ff539, 0xafd3eb54, 0x8a681e8f, 0x724bd78e, 0x57f02255, 0x393c3c38, 0x1c87c9e3, 0x9cf06899, 0xb94b9d42, 0xd787832f, 0xf23c76f4, 0x0a1fbff5, 0x2fa44a2e, 0x41685443, 0x64d3a198, 0xb4c3b0b0, 0x9178456b, 0xffb45b06, 0xda0faedd, 0x222c67dc, 0x07979207, 0x695b8c6a, 0x4ce079b1, 0x6c58b86f, 0x49e34db4, 0x272f53d9, 0x0294a602, 0xfab76f03, 0xdf0c9ad8, 0xb1c084b5, 0x947b716e, 0x446b6046, 0x61d0959d, 0x0f1c8bf0, 0x2aa77e2b, 0xd284b72a, 0xf73f42f1, 0x99f35c9c, 0xbc48a947, 0x3c3f083d, 0x1984fde6, 0x7748e38b, 0x52f31650, 0xaad0df51, 0x8f6b2a8a, 0xe1a734e7, 0xc41cc13c, 0x140cd014, 0x31b725cf, 0x5f7b3ba2, 0x7ac0ce79, 0x82e30778, 0xa758f2a3, 0xc994ecce, 0xec2f1915 }
};
