defaults:
 - rollout_lh: calvin

ema:
  _target_: mode.callbacks.ema.EMA
  decay: 0.999
  start_step: 0
  save_ema_weights_in_callback_state: True
  evaluate_ema_weights_instead: True
  power: 0.6666666666666666
  inv_gamma: 1.0
  min_value: 0.0
  max_value: 0.9999

checkpoint:
  _target_: pytorch_lightning.callbacks.ModelCheckpoint
  save_top_k: 1
  verbose: True
  monitor: eval_lh/avg_seq_len
  mode: max
  dirpath: saved_models
  filename: '{epoch:02d}_{eval_lh/avg_seq_len:.2f}'
  every_n_epochs: ${callbacks.rollout_lh.rollout_freq}