eval_cfg_overwrite:
  datamodule:
    datasets:
      lang_dataset:
        lang_folder: lang_annotations
  model:
    num_sampling_steps: 10

train_folder: /home/<USER>/code/MoDE_Diffusion_Policy/pretrained/
checkpoint: libero_90=0
device: 0


log_dir: /home/<USER>/code/MoDE_Diffusion_Policy/outputs/libero_90_test
dataset_path: /home/<USER>/code/mdt_policy/dataset/calvin_debug_dataset
num_videos: 30
debug: False

log_wandb: True
wandb_entity: omeryagmurlu

num_sampling_steps: null
sampler_type: null
multistep: null
sigma_min: null
sigma_max: null
noise_scheduler: null

num_sequences: 50
max_steps: 520
n_eval: 20
task_embedding_format: clip

benchmark_name: libero_90 # [LIBERO_SPATIAL, LIBERO_OBJECT, LIBERO_GOAL, LIBERO_90, LIBERO_10]