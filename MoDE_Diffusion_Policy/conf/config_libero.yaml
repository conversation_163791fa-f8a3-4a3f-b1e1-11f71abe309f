defaults:
  - callbacks: libero
  - datamodule: libero
  - model: mode_agent
  - override hydra/job_logging: colorlog
  - override hydra/hydra_logging: colorlog

root_data_dir: /home/<USER>/code/MoDE_Calvin/dataset/task_ABC_D
lang_folder: lang_clip_resnet50
vis_clip_model_name: ViT-B/16
clip_lang_model_name: ViT-B/32

log_dir: ./logs
slurm: false
future_range: 29
seed: 242
device: 'cuda'
batch_size: 128 # 38 # 128
devices: 2
goal_window_size: 1
act_dim: 7
proprio_dims: 9
obs_dim: 512
goal_dim: 512
obs_seq_len: 1
act_seq_len: 10
multistep: ${act_seq_len}
p_last_state: 0
gen_img_res: 112
max_epochs: 20
rollout_lh_skip_epochs: 9
num_workers: 1
benchmark_name: ${libero_benchmark} 
libero_benchmark: libero_10


trainer:
  devices: ${devices}
  precision: bf16
  max_epochs: ${max_epochs}
  sync_batchnorm: False
  accelerator: auto
  limit_train_batches: 1000
  limit_val_batches: 4
  # gradient_clip_val: 1

logger:
  _target_: pytorch_lightning.loggers.WandbLogger
  save_dir: .
  name: logger
  group: mode
  log_model: false
  project: ${libero_benchmark}
  entity: omeryagmurlu
  id: ???


hydra:
  run:
    dir: ${log_dir}/runs/${now:%Y-%m-%d}/${now:%H-%M-%S}
  sweep:
    dir: ${log_dir}/runs/${now:%Y-%m-%d}/${now:%H-%M-%S}
    subdir: ${hydra.job.override_dirname}
  job:
    config:
      override_dirname:
        exclude_keys:
          - log_dir
          - datamodule.root_data_dir
          - trainer.gpus
          - datamodule.num_workers
          - trainer.limit_train_batches
          - trainer.limit_val_batches
