defaults:
  - callbacks: calvin
  - datamodule: calvin
  - model: mode_agent
  - override hydra/job_logging: colorlog
  - override hydra/hydra_logging: colorlog


root_data_dir: /hkfs/work/workspace/scratch/ft4740-play3/data/task_ABC_D
lang_folder: lang_clip_resnet50
vis_clip_model_name: ViT-B/16
clip_lang_model_name: ViT-B/32

log_dir: ./logs
slurm: false
seed: 242
device: 'cuda'
batch_size: 64 # 38 # 128
devices: 4
act_dim: 7
proprio_dims: 7
obs_dim: 512
goal_dim: 512
obs_seq_len: 1
act_seq_len: 10
multistep: 10 #${act_seq_len}
p_last_state: 0
gen_img_res: 112
max_epochs: 20
rollout_lh_skip_epochs: 9
num_workers: 12
benchmark_name: calvin_abc # calvin_abcd
use_extracted_rel_actions: true

trainer:
  devices: ${devices}
  precision: bf16
  max_epochs: ${max_epochs}
  sync_batchnorm: True
  accelerator: gpu
  strategy: "ddp" 
  limit_train_batches: 1000
  limit_val_batches: 4



logger:
  _target_: pytorch_lightning.loggers.WandbLogger
  save_dir: .
  name: logger
  group: mode
  log_model: false
  project: ${benchmark_name}
  entity: bennoq
  id: ???


hydra:
  run:
    dir: ${log_dir}/runs/${now:%Y-%m-%d}/${now:%H-%M-%S}_seed${seed}
  sweep:
    dir: ${log_dir}/runs/${now:%Y-%m-%d}/${now:%H-%M-%S}
    subdir: ${hydra.job.override_dirname}
  job:
    config:
      override_dirname:
        exclude_keys:
          - log_dir
          - datamodule.root_data_dir
          - trainer.gpus
          - datamodule.num_workers
          - trainer.limit_train_batches
          - trainer.limit_val_batches
