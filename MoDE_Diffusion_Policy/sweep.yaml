program: mode/evaluation/mode_evaluate.py
method: grid
metric:
  goal: maximize
  name: avrg_performance/avg_seq_len

parameters:
  num_sequences:
    value: 1000
  num_sampling_steps:
    # values: [10, 30]
    # values: [1, 3, 5, 10, 20]
    values: [5]
  sampler_type:
    # values: ['euler', 'ancestral', 'euler_ancestral', 'dpmpp_2m', 'dpmpp_2m_sde', 'ddim']
    values: ['ddim']
  sigma_min:
    # values: [0.001, 0.01, 0.1, 0.5, 1.0, 5.0, 10.0]
    values: [1.0]
    # values: [1.0, 0.5]
  sigma_max:
    values: [80]
  noise_scheduler:
    values: ['linear']
  checkpoint:
    values: ['abc_ema_correct_seed1\=0', 'abc_ema_correct_seed2\=0']
  

command:
  - ${env}
  - python
  - ${program}
  - ${args_no_hyphens}